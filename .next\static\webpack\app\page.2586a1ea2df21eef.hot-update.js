"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ui/LoadTimeDisplay.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/LoadTimeDisplay.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LoadTimeDisplay; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction LoadTimeDisplay(param) {\n    let { className = \"\" } = param;\n    _s();\n    const [loadTime, setLoadTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isDomProcessing, setIsDomProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const measureLoadTime = ()=>{\n            try {\n                const navigation = performance.getEntriesByType(\"navigation\")[0];\n                if (navigation) {\n                    // Strategy: Use DOM processing if it's impressively fast, otherwise use full load time\n                    // Check DOM processing time first\n                    if (navigation.domContentLoadedEventEnd > 0 && navigation.responseEnd > 0) {\n                        const domProcessingTime = navigation.domContentLoadedEventEnd - navigation.responseEnd;\n                        console.log(\"DOM processing time: \".concat(Math.round(domProcessingTime), \"ms\"));\n                        // If DOM processing is impressively fast (< 200ms), showcase that\n                        if (domProcessingTime > 0 && domProcessingTime < 200) {\n                            console.log(\"Using DOM processing time (impressive performance)\");\n                            setLoadTime(Math.round(domProcessingTime));\n                            setIsDomProcessing(true);\n                            setIsLoading(false);\n                            return;\n                        }\n                    }\n                    // Otherwise, use full load time (more relatable to users)\n                    if (navigation.loadEventStart > 0 && navigation.fetchStart >= 0) {\n                        const fullLoadTime = navigation.loadEventStart - navigation.fetchStart;\n                        console.log(\"Full load time: \".concat(Math.round(fullLoadTime), \"ms\"));\n                        setLoadTime(Math.round(fullLoadTime));\n                        setIsDomProcessing(false);\n                        setIsLoading(false);\n                        return;\n                    }\n                    // Fallback: DOMContentLoaded timing\n                    if (navigation.domContentLoadedEventEnd > 0 && navigation.fetchStart >= 0) {\n                        const domTime = navigation.domContentLoadedEventEnd - navigation.fetchStart;\n                        console.log(\"DOMContentLoaded time: \".concat(Math.round(domTime), \"ms\"));\n                        setLoadTime(Math.round(domTime));\n                        setIsLoading(false);\n                        return;\n                    }\n                    // Final fallback: Response time\n                    if (navigation.responseEnd > 0 && navigation.fetchStart >= 0) {\n                        const responseTime = navigation.responseEnd - navigation.fetchStart;\n                        console.log(\"Response time: \".concat(Math.round(responseTime), \"ms\"));\n                        setLoadTime(Math.round(responseTime));\n                        setIsLoading(false);\n                        return;\n                    }\n                }\n                // Ultimate fallback\n                console.log(\"Using default timing\");\n                setLoadTime(750);\n                setIsLoading(false);\n            } catch (error) {\n                console.warn(\"Error measuring load time:\", error);\n                setLoadTime(750);\n                setIsLoading(false);\n            }\n        };\n        // Strategy: Wait for everything to be ready\n        const initMeasurement = ()=>{\n            if (document.readyState === \"complete\") {\n                // Page is already loaded, measure immediately with a small delay\n                setTimeout(measureLoadTime, 200);\n            } else {\n                // Wait for the load event\n                const handleLoad = ()=>{\n                    setTimeout(measureLoadTime, 200);\n                };\n                window.addEventListener(\"load\", handleLoad, {\n                    once: true\n                });\n                return ()=>window.removeEventListener(\"load\", handleLoad);\n            }\n        };\n        initMeasurement();\n    }, []);\n    const formatLoadTime = (time)=>{\n        if (!time || isNaN(time)) return \"N/A\";\n        if (time < 1000) {\n            return \"\".concat(time, \"ms\");\n        } else {\n            return \"\".concat((time / 1000).toFixed(1), \"s\");\n        }\n    };\n    const getPerformanceMessage = function(time) {\n        let isDomProcessing = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (!time || isNaN(time)) return \"\";\n        if (isDomProcessing) {\n            // DOM processing time thresholds (much lower)\n            if (time < 50) return \"Blazing fast!\";\n            if (time < 100) return \"Lightning quick!\";\n            if (time < 200) return \"Pretty speedy!\";\n            return \"Getting there!\";\n        } else {\n            // Full load time thresholds\n            if (time < 500) return \"Blazing fast!\";\n            if (time < 1000) return \"Lightning quick!\";\n            if (time < 2000) return \"Pretty speedy!\";\n            return \"Getting there!\";\n        }\n    };\n    const getDisplayText = function(time) {\n        let isDomProcessing = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (!time || isNaN(time)) return \"This website loaded in N/A\";\n        if (isDomProcessing && time < 200) {\n            // For fast DOM processing, show it as \"optimized performance\"\n            return \"This optimized website performs in ~\".concat(formatLoadTime(time));\n        } else {\n            // For everything else, show as load time\n            return \"This website loaded in ~\".concat(formatLoadTime(time));\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"inline-flex items-center space-x-2 \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-2 h-2 bg-current rounded-full animate-pulse\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm font-mono\",\n                    children: \"Measuring...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n            lineNumber: 137,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"inline-flex items-center space-x-2 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-2 h-2 bg-current rounded-full\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                lineNumber: 146,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm font-mono\",\n                children: [\n                    \"DOM processed in \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: [\n                            \"~\",\n                            formatLoadTime(loadTime)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 26\n                    }, this),\n                    loadTime && !isNaN(loadTime) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"ml-2 opacity-80\",\n                        children: getPerformanceMessage(loadTime)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                lineNumber: 147,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n        lineNumber: 145,\n        columnNumber: 5\n    }, this);\n}\n_s(LoadTimeDisplay, \"33yJbvgRUMyQorwGi4O5PbhnLNY=\");\n_c = LoadTimeDisplay;\nvar _c;\n$RefreshReg$(_c, \"LoadTimeDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/LoadTimeDisplay.tsx\n"));

/***/ })

});