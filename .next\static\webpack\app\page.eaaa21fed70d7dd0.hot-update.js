"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ui/LoadTimeDisplay.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/LoadTimeDisplay.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LoadTimeDisplay; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction LoadTimeDisplay(param) {\n    let { className = \"\" } = param;\n    _s();\n    const [metrics, setMetrics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        domProcessing: null,\n        networkTime: null,\n        totalLoad: null\n    });\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const measurePerformance = ()=>{\n            try {\n                const navigation = performance.getEntriesByType(\"navigation\")[0];\n                if (navigation) {\n                    const newMetrics = {\n                        domProcessing: null,\n                        networkTime: null,\n                        totalLoad: null\n                    };\n                    // DOM Processing Time (website optimization)\n                    if (navigation.domContentLoadedEventEnd > 0 && navigation.responseEnd > 0) {\n                        newMetrics.domProcessing = Math.round(navigation.domContentLoadedEventEnd - navigation.responseEnd);\n                    }\n                    // Network Time (connection quality)\n                    if (navigation.responseEnd > 0 && navigation.fetchStart >= 0) {\n                        newMetrics.networkTime = Math.round(navigation.responseEnd - navigation.fetchStart);\n                    }\n                    // Total Load Time (overall experience)\n                    if (navigation.loadEventStart > 0 && navigation.fetchStart >= 0) {\n                        newMetrics.totalLoad = Math.round(navigation.loadEventStart - navigation.fetchStart);\n                    }\n                    console.log(\"Performance metrics:\", newMetrics);\n                    setMetrics(newMetrics);\n                    setIsLoading(false);\n                } else {\n                    // Fallback metrics\n                    setMetrics({\n                        domProcessing: 50,\n                        networkTime: 150,\n                        totalLoad: 750\n                    });\n                    setIsLoading(false);\n                }\n            } catch (error) {\n                console.warn(\"Error measuring performance:\", error);\n                setMetrics({\n                    domProcessing: 50,\n                    networkTime: 150,\n                    totalLoad: 750\n                });\n                setIsLoading(false);\n            }\n        };\n        // Strategy: Wait for everything to be ready\n        const initMeasurement = ()=>{\n            if (document.readyState === \"complete\") {\n                // Page is already loaded, measure immediately with a small delay\n                setTimeout(measureLoadTime, 200);\n            } else {\n                // Wait for the load event\n                const handleLoad = ()=>{\n                    setTimeout(measureLoadTime, 200);\n                };\n                window.addEventListener(\"load\", handleLoad, {\n                    once: true\n                });\n                return ()=>window.removeEventListener(\"load\", handleLoad);\n            }\n        };\n        initMeasurement();\n    }, []);\n    const formatLoadTime = (time)=>{\n        if (!time || isNaN(time)) return \"N/A\";\n        if (time < 1000) {\n            return \"\".concat(time, \"ms\");\n        } else {\n            return \"\".concat((time / 1000).toFixed(1), \"s\");\n        }\n    };\n    const getPerformanceMessage = function(time) {\n        let isDomProcessing1 = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (!time || isNaN(time)) return \"\";\n        if (isDomProcessing1) {\n            // DOM processing time thresholds (much lower)\n            if (time < 50) return \"Blazing fast!\";\n            if (time < 100) return \"Lightning quick!\";\n            if (time < 200) return \"Pretty speedy!\";\n            return \"Getting there!\";\n        } else {\n            // Full load time thresholds\n            if (time < 500) return \"Blazing fast!\";\n            if (time < 1000) return \"Lightning quick!\";\n            if (time < 2000) return \"Pretty speedy!\";\n            return \"Getting there!\";\n        }\n    };\n    const getDisplayText = function(time) {\n        let isDomProcessing1 = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (!time || isNaN(time)) return \"This website loaded in N/A\";\n        if (isDomProcessing1 && time < 200) {\n            // For fast DOM processing, show it as \"optimized performance\"\n            return \"This optimized website performs in ~\".concat(formatLoadTime(time));\n        } else {\n            // For everything else, show as load time\n            return \"This website loaded in ~\".concat(formatLoadTime(time));\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"inline-flex items-center space-x-2 \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-2 h-2 bg-current rounded-full animate-pulse\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm font-mono\",\n                    children: \"Measuring...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n            lineNumber: 133,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"inline-flex items-center space-x-2 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-2 h-2 bg-current rounded-full\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm font-mono\",\n                children: [\n                    getDisplayText(loadTime, isDomProcessing),\n                    loadTime && !isNaN(loadTime) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"ml-2 opacity-80\",\n                        children: getPerformanceMessage(loadTime, isDomProcessing)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n        lineNumber: 141,\n        columnNumber: 5\n    }, this);\n}\n_s(LoadTimeDisplay, \"6FitVEwf91HySuhJ+VaOEnPX+Os=\");\n_c = LoadTimeDisplay;\nvar _c;\n$RefreshReg$(_c, \"LoadTimeDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/LoadTimeDisplay.tsx\n"));

/***/ })

});