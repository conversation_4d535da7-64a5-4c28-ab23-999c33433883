"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ui/LoadTimeDisplay.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/LoadTimeDisplay.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LoadTimeDisplay; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction LoadTimeDisplay(param) {\n    let { className = \"\" } = param;\n    _s();\n    const [metrics, setMetrics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        domProcessing: null,\n        networkTime: null,\n        totalLoad: null\n    });\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const measurePerformance = ()=>{\n            try {\n                const navigation = performance.getEntriesByType(\"navigation\")[0];\n                if (navigation) {\n                    const newMetrics = {\n                        domProcessing: null,\n                        networkTime: null,\n                        totalLoad: null\n                    };\n                    // DOM Processing Time (website optimization)\n                    if (navigation.domContentLoadedEventEnd > 0 && navigation.responseEnd > 0) {\n                        newMetrics.domProcessing = Math.round(navigation.domContentLoadedEventEnd - navigation.responseEnd);\n                    }\n                    // Network Time (connection quality)\n                    if (navigation.responseEnd > 0 && navigation.fetchStart >= 0) {\n                        newMetrics.networkTime = Math.round(navigation.responseEnd - navigation.fetchStart);\n                    }\n                    // Total Load Time (overall experience)\n                    if (navigation.loadEventStart > 0 && navigation.fetchStart >= 0) {\n                        newMetrics.totalLoad = Math.round(navigation.loadEventStart - navigation.fetchStart);\n                    }\n                    console.log(\"Performance metrics:\", newMetrics);\n                    setMetrics(newMetrics);\n                    setIsLoading(false);\n                } else {\n                    // Fallback metrics\n                    setMetrics({\n                        domProcessing: 50,\n                        networkTime: 150,\n                        totalLoad: 750\n                    });\n                    setIsLoading(false);\n                }\n            } catch (error) {\n                console.warn(\"Error measuring performance:\", error);\n                setMetrics({\n                    domProcessing: 50,\n                    networkTime: 150,\n                    totalLoad: 750\n                });\n                setIsLoading(false);\n            }\n        };\n        // Strategy: Wait for everything to be ready\n        const initMeasurement = ()=>{\n            if (document.readyState === \"complete\") {\n                // Page is already loaded, measure immediately with a small delay\n                setTimeout(measurePerformance, 200);\n            } else {\n                // Wait for the load event\n                const handleLoad = ()=>{\n                    setTimeout(measurePerformance, 200);\n                };\n                window.addEventListener(\"load\", handleLoad, {\n                    once: true\n                });\n                return ()=>window.removeEventListener(\"load\", handleLoad);\n            }\n        };\n        initMeasurement();\n    }, []);\n    const formatTime = (time)=>{\n        if (!time || isNaN(time)) return \"N/A\";\n        if (time < 1000) {\n            return \"\".concat(time, \"ms\");\n        } else {\n            return \"\".concat((time / 1000).toFixed(1), \"s\");\n        }\n    };\n    const getMetricStatus = (metric, time)=>{\n        if (!time || isNaN(time)) return {\n            status: \"unknown\",\n            message: \"\",\n            note: \"\"\n        };\n        switch(metric){\n            case \"dom\":\n                if (time < 50) return {\n                    status: \"excellent\",\n                    message: \"Blazing fast!\",\n                    note: \"\"\n                };\n                if (time < 100) return {\n                    status: \"good\",\n                    message: \"Lightning quick!\",\n                    note: \"\"\n                };\n                if (time < 200) return {\n                    status: \"fair\",\n                    message: \"Pretty good!\",\n                    note: \"\"\n                };\n                return {\n                    status: \"slow\",\n                    message: \"Could be faster\",\n                    note: \"Website optimization needed\"\n                };\n            case \"network\":\n                if (time < 100) return {\n                    status: \"excellent\",\n                    message: \"Excellent connection!\",\n                    note: \"\"\n                };\n                if (time < 300) return {\n                    status: \"good\",\n                    message: \"Good connection\",\n                    note: \"\"\n                };\n                if (time < 1000) return {\n                    status: \"fair\",\n                    message: \"Moderate connection\",\n                    note: \"\"\n                };\n                return {\n                    status: \"slow\",\n                    message: \"Slow connection\",\n                    note: \"Check your internet connection\"\n                };\n            case \"total\":\n                if (time < 500) return {\n                    status: \"excellent\",\n                    message: \"Blazing fast!\",\n                    note: \"\"\n                };\n                if (time < 1000) return {\n                    status: \"good\",\n                    message: \"Lightning quick!\",\n                    note: \"\"\n                };\n                if (time < 2000) return {\n                    status: \"fair\",\n                    message: \"Pretty speedy!\",\n                    note: \"\"\n                };\n                return {\n                    status: \"slow\",\n                    message: \"Taking a while\",\n                    note: \"Is your internet connection stable?\"\n                };\n            default:\n                return {\n                    status: \"unknown\",\n                    message: \"\",\n                    note: \"\"\n                };\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"excellent\":\n                return \"text-green-400\";\n            case \"good\":\n                return \"text-bauhaus-yellow\";\n            case \"fair\":\n                return \"text-orange-400\";\n            case \"slow\":\n                return \"text-red-400\";\n            default:\n                return \"text-gray-400\";\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"inline-flex items-center space-x-2 \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-2 h-2 bg-current rounded-full animate-pulse\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm font-mono\",\n                    children: \"Measuring performance...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n            lineNumber: 139,\n            columnNumber: 7\n        }, this);\n    }\n    const domStatus = getMetricStatus(\"dom\", metrics.domProcessing);\n    const networkStatus = getMetricStatus(\"network\", metrics.networkTime);\n    const totalStatus = getMetricStatus(\"total\", metrics.totalLoad);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-3 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-2 h-2 bg-current rounded-full\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-mono\",\n                        children: [\n                            \"This website loaded in ~\",\n                            formatTime(metrics.totalLoad),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-2 opacity-80\",\n                                children: totalStatus.message\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2 text-xs font-mono\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-300\",\n                                children: \"Website optimization:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: getStatusColor(domStatus.status),\n                                        children: formatTime(metrics.domProcessing)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 13\n                                    }, this),\n                                    domStatus.note && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-400 italic\",\n                                        children: [\n                                            \"(\",\n                                            domStatus.note,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-300\",\n                                children: \"Network connection:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: getStatusColor(networkStatus.status),\n                                        children: formatTime(metrics.networkTime)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 13\n                                    }, this),\n                                    networkStatus.note && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-400 italic\",\n                                        children: [\n                                            \"(\",\n                                            networkStatus.note,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-300\",\n                                children: \"Total load time:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: getStatusColor(totalStatus.status),\n                                        children: formatTime(metrics.totalLoad)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 13\n                                    }, this),\n                                    totalStatus.note && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-400 italic\",\n                                        children: [\n                                            \"(\",\n                                            totalStatus.note,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                lineNumber: 164,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n        lineNumber: 151,\n        columnNumber: 5\n    }, this);\n}\n_s(LoadTimeDisplay, \"6FitVEwf91HySuhJ+VaOEnPX+Os=\");\n_c = LoadTimeDisplay;\nvar _c;\n$RefreshReg$(_c, \"LoadTimeDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/LoadTimeDisplay.tsx\n"));

/***/ })

});