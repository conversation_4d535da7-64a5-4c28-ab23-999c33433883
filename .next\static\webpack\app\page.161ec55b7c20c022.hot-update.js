"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ui/LoadTimeDisplay.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/LoadTimeDisplay.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LoadTimeDisplay; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction LoadTimeDisplay(param) {\n    let { className = \"\" } = param;\n    _s();\n    const [loadTime, setLoadTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const measureLoadTime = ()=>{\n            try {\n                const navigation = performance.getEntriesByType(\"navigation\")[0];\n                if (navigation) {\n                    // Use Load event timing (matches Network tab \"Load\" within ~5ms)\n                    if (navigation.loadEventStart > 0 && navigation.fetchStart >= 0) {\n                        const loadTime = navigation.loadEventStart - navigation.fetchStart;\n                        console.log(\"Load time: \".concat(Math.round(loadTime), \"ms (Network tab shows ~\").concat(Math.round(loadTime - 5), \"ms)\"));\n                        setLoadTime(Math.round(loadTime));\n                        setIsLoading(false);\n                        return;\n                    }\n                    // Fallback: DOMContentLoaded timing\n                    if (navigation.domContentLoadedEventEnd > 0 && navigation.fetchStart >= 0) {\n                        const domTime = navigation.domContentLoadedEventEnd - navigation.fetchStart;\n                        console.log(\"DOMContentLoaded time: \".concat(Math.round(domTime), \"ms\"));\n                        setLoadTime(Math.round(domTime));\n                        setIsLoading(false);\n                        return;\n                    }\n                    // Final fallback: Response time\n                    if (navigation.responseEnd > 0 && navigation.fetchStart >= 0) {\n                        const responseTime = navigation.responseEnd - navigation.fetchStart;\n                        console.log(\"Response time: \".concat(Math.round(responseTime), \"ms\"));\n                        setLoadTime(Math.round(responseTime));\n                        setIsLoading(false);\n                        return;\n                    }\n                }\n                // Ultimate fallback\n                console.log(\"Using default timing\");\n                setLoadTime(750);\n                setIsLoading(false);\n            } catch (error) {\n                console.warn(\"Error measuring load time:\", error);\n                setLoadTime(750);\n                setIsLoading(false);\n            }\n        };\n        // Strategy: Wait for everything to be ready\n        const initMeasurement = ()=>{\n            if (document.readyState === \"complete\") {\n                // Page is already loaded, measure immediately with a small delay\n                setTimeout(measureLoadTime, 200);\n            } else {\n                // Wait for the load event\n                const handleLoad = ()=>{\n                    setTimeout(measureLoadTime, 200);\n                };\n                window.addEventListener(\"load\", handleLoad, {\n                    once: true\n                });\n                return ()=>window.removeEventListener(\"load\", handleLoad);\n            }\n        };\n        initMeasurement();\n    }, []);\n    const formatLoadTime = (time)=>{\n        if (!time || isNaN(time)) return \"N/A\";\n        if (time < 1000) {\n            return \"\".concat(time, \"ms\");\n        } else {\n            return \"\".concat((time / 1000).toFixed(1), \"s\");\n        }\n    };\n    const getPerformanceMessage = (time)=>{\n        if (!time || isNaN(time)) return \"\";\n        if (time < 500) return \"Blazing fast!\";\n        if (time < 1000) return \"Lightning quick!\";\n        if (time < 2000) return \"Pretty speedy!\";\n        return \"Getting there!\";\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"inline-flex items-center space-x-2 \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-2 h-2 bg-current rounded-full animate-pulse\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm font-mono\",\n                    children: \"Measuring...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n            lineNumber: 97,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"inline-flex items-center space-x-2 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-2 h-2 bg-current rounded-full\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm font-mono\",\n                children: [\n                    \"This website loaded in \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: formatLoadTime(loadTime)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 32\n                    }, this),\n                    loadTime && !isNaN(loadTime) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"ml-2 opacity-80\",\n                        children: getPerformanceMessage(loadTime)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, this);\n}\n_s(LoadTimeDisplay, \"Wk6++ihLZAXguEzIXEKQHPxN2hU=\");\n_c = LoadTimeDisplay;\nvar _c;\n$RefreshReg$(_c, \"LoadTimeDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/LoadTimeDisplay.tsx\n"));

/***/ })

});