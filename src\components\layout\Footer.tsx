'use client'

import Image from 'next/image'
import Link from 'next/link'
import {
  AnimatedSoftCircle,
  AnimatedRoundedRectangle,
  AnimatedTriangle,
  AnimatedSoftGrid,
  AnimatedBlob,
  AnimatedPill,
  AnimatedQuarterCircle
} from '@/components/shapes/AnimatedShapes'

export default function Footer() {
  return (
    <footer className="relative w-full bg-bauhaus-black text-brand-background overflow-hidden">
      {/* Background Grid Pattern */}
      <div className="absolute inset-0 opacity-10">
        <AnimatedSoftGrid
          className="w-full h-full text-brand-background"
          opacity="default"
          animationPreset="drift"
          animationIndex={200}
        />
      </div>

      {/* Main Footer Content */}
      <div className="relative z-10 px-6 md:px-12 lg:px-24 py-16 md:py-20">
        <div className="max-w-7xl mx-auto">

          {/* Top Section - Logo, Navigation, and CTA */}
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-12 lg:gap-16 mb-16">

            {/* Logo and Tagline */}
            <div className="lg:col-span-4 space-y-6">
              <div className="relative">
                <Image
                  src="/images/logo.png"
                  alt="Navhaus"
                  width={140}
                  height={45}
                  className="h-10 w-auto brightness-0 invert"
                />
                {/* Decorative accent */}
                <div className="absolute -top-2 -right-8">
                  <AnimatedSoftCircle
                    size="sm"
                    color="red"
                    className="w-4 h-4"
                    animationPreset="gentle"
                    animationIndex={201}
                  />
                </div>
              </div>
              <div className="space-y-4">
                <p className="text-xl font-bold text-brand-background">
                  What matters, made real.
                </p>
                <p className="text-gray-300 leading-relaxed max-w-sm">
                  We build bold, efficient, and meaningful digital experiences.
                  Nothing more, nothing less.
                </p>
              </div>
            </div>

            {/* Navigation Links */}
            <div className="lg:col-span-4 space-y-8">
              <div>
                <h3 className="text-lg font-bold text-bauhaus-yellow mb-6">Navigate</h3>
                <nav className="space-y-4">
                  <Link href="/" className="block text-brand-background hover:text-bauhaus-yellow transition-colors duration-200 font-medium">
                    Home
                  </Link>
                  <Link href="/about" className="block text-brand-background hover:text-bauhaus-yellow transition-colors duration-200 font-medium">
                    About
                  </Link>
                  <Link href="/work" className="block text-brand-background hover:text-bauhaus-yellow transition-colors duration-200 font-medium">
                    Work
                  </Link>
                  <Link href="/contact" className="block text-brand-background hover:text-bauhaus-yellow transition-colors duration-200 font-medium">
                    Contact
                  </Link>
                </nav>
              </div>

              {/* Services Quick Links */}
              <div>
                <h3 className="text-lg font-bold text-bauhaus-blue mb-6">Services</h3>
                <div className="space-y-4 text-sm">
                  <div className="text-gray-300">WordPress Development</div>
                  <div className="text-gray-300">React & Next.js Apps</div>
                  <div className="text-gray-300">Performance Optimization</div>
                  <div className="text-gray-300">Ongoing Maintenance</div>
                </div>
              </div>
            </div>

            {/* Contact and CTA */}
            <div className="lg:col-span-4 space-y-8">
              <div>
                <h3 className="text-lg font-bold text-bauhaus-red mb-6">Let's Build</h3>
                <div className="space-y-4">
                  <p className="text-brand-background font-medium">
                    Ready to build something?
                  </p>
                  <a
                    href="mailto:<EMAIL>"
                    className="inline-block text-bauhaus-red hover:text-bauhaus-yellow transition-colors duration-200 font-bold text-lg"
                  >
                    <EMAIL>
                  </a>
                  <div className="pt-4">
                    <Link
                      href="/contact"
                      className="btn-primary bg-brand-background text-bauhaus-black border-brand-background hover:bg-transparent hover:text-brand-background inline-block"
                    >
                      Start Your Project
                    </Link>
                  </div>
                </div>
              </div>

              {/* Response Time Indicator */}
              <div className="flex items-center space-x-3 text-sm text-gray-400">
                <div className="flex space-x-1">
                  <AnimatedPill color="red" className="w-2 h-2" animationPreset="gentle" animationIndex={202} />
                  <AnimatedPill color="yellow" className="w-2 h-2" animationPreset="gentle" animationIndex={203} />
                  <AnimatedPill color="blue" className="w-2 h-2" animationPreset="gentle" animationIndex={204} />
                </div>
                <span>Usually responds within 24 hours</span>
              </div>
            </div>
          </div>

          {/* Decorative Divider with Animated Elements */}
          <div className="relative py-12 mb-12">
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-full h-px bg-gray-800"></div>
            </div>
            <div className="relative flex justify-center space-x-8">
              <AnimatedRoundedRectangle
                width="lg"
                height="sm"
                color="red"
                className="w-16 h-4"
                animationPreset="flowing"
                animationIndex={205}
              />
              <AnimatedSoftCircle
                size="md"
                color="yellow"
                className="w-8 h-8"
                animationPreset="pulse"
                animationIndex={206}
              />
              <AnimatedTriangle
                size="md"
                color="blue"
                direction="up"
                className="w-8 h-8"
                animationPreset="dynamic"
                animationIndex={207}
              />
            </div>
          </div>

          {/* Bottom Section - Copyright and Credits */}
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-sm text-gray-500">
              © {new Date().getFullYear()} Navhaus. All rights reserved.
            </div>

            <div className="flex items-center space-x-6 text-sm text-gray-500">
              <span>Built with intention</span>
              <div className="flex space-x-2">
                <div className="w-2 h-2 bg-bauhaus-red rounded-full"></div>
                <div className="w-2 h-2 bg-bauhaus-yellow rounded-full"></div>
                <div className="w-2 h-2 bg-bauhaus-blue rounded-full"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Floating Background Elements */}
      <div className="absolute top-16 left-16 opacity-20">
        <AnimatedBlob
          color="red"
          className="w-24 h-24"
          animationPreset="drift"
          animationIndex={208}
        />
      </div>
      <div className="absolute bottom-20 right-20 opacity-15">
        <AnimatedQuarterCircle
          color="blue"
          corner="top-left"
          className="w-32 h-32"
          animationPreset="gentle"
          animationIndex={209}
        />
      </div>
      <div className="absolute top-1/2 right-8 opacity-10">
        <AnimatedRoundedRectangle
          width="lg"
          height="xl"
          color="yellow"
          className="w-8 h-24"
          animationPreset="float"
          animationIndex={210}
        />
      </div>
      <div className="absolute bottom-8 left-1/4 opacity-15">
        <AnimatedSoftCircle
          size="lg"
          color="red"
          className="w-16 h-16"
          animationPreset="energetic"
          animationIndex={211}
        />
      </div>
      <div className="absolute top-20 right-1/3 opacity-10">
        <AnimatedTriangle
          size="lg"
          color="yellow"
          direction="up"
          className="w-12 h-12"
          animationPreset="pulse"
          animationIndex={212}
        />
      </div>
    </footer>
  )
}
