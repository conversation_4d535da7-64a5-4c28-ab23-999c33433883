"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ui/LoadTimeDisplay.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/LoadTimeDisplay.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LoadTimeDisplay; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction LoadTimeDisplay(param) {\n    let { className = \"\" } = param;\n    _s();\n    const [loadTime, setLoadTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const measureLoadTime = ()=>{\n            try {\n                const navigation = performance.getEntriesByType(\"navigation\")[0];\n                if (navigation) {\n                    // Option 1: DOM Processing Time (website performance, not network)\n                    // This measures how fast your website processes after HTML arrives\n                    if (navigation.domContentLoadedEventEnd > 0 && navigation.responseEnd > 0) {\n                        const domProcessingTime = navigation.domContentLoadedEventEnd - navigation.responseEnd;\n                        console.log(\"DOM processing time: \".concat(Math.round(domProcessingTime), \"ms (website performance)\"));\n                        if (domProcessingTime > 0 && domProcessingTime < 10000) {\n                            setLoadTime(Math.round(domProcessingTime));\n                            setIsLoading(false);\n                            return;\n                        }\n                    }\n                    // Option 2: Full Load Time (includes network - current approach)\n                    if (navigation.loadEventStart > 0 && navigation.fetchStart >= 0) {\n                        const loadTime = navigation.loadEventStart - navigation.fetchStart;\n                        console.log(\"Full load time: \".concat(Math.round(loadTime), \"ms (includes network)\"));\n                        setLoadTime(Math.round(loadTime));\n                        setIsLoading(false);\n                        return;\n                    }\n                    // Fallback: DOMContentLoaded timing\n                    if (navigation.domContentLoadedEventEnd > 0 && navigation.fetchStart >= 0) {\n                        const domTime = navigation.domContentLoadedEventEnd - navigation.fetchStart;\n                        console.log(\"DOMContentLoaded time: \".concat(Math.round(domTime), \"ms\"));\n                        setLoadTime(Math.round(domTime));\n                        setIsLoading(false);\n                        return;\n                    }\n                    // Final fallback: Response time\n                    if (navigation.responseEnd > 0 && navigation.fetchStart >= 0) {\n                        const responseTime = navigation.responseEnd - navigation.fetchStart;\n                        console.log(\"Response time: \".concat(Math.round(responseTime), \"ms\"));\n                        setLoadTime(Math.round(responseTime));\n                        setIsLoading(false);\n                        return;\n                    }\n                }\n                // Ultimate fallback\n                console.log(\"Using default timing\");\n                setLoadTime(750);\n                setIsLoading(false);\n            } catch (error) {\n                console.warn(\"Error measuring load time:\", error);\n                setLoadTime(750);\n                setIsLoading(false);\n            }\n        };\n        // Strategy: Wait for everything to be ready\n        const initMeasurement = ()=>{\n            if (document.readyState === \"complete\") {\n                // Page is already loaded, measure immediately with a small delay\n                setTimeout(measureLoadTime, 200);\n            } else {\n                // Wait for the load event\n                const handleLoad = ()=>{\n                    setTimeout(measureLoadTime, 200);\n                };\n                window.addEventListener(\"load\", handleLoad, {\n                    once: true\n                });\n                return ()=>window.removeEventListener(\"load\", handleLoad);\n            }\n        };\n        initMeasurement();\n    }, []);\n    const formatLoadTime = (time)=>{\n        if (!time || isNaN(time)) return \"N/A\";\n        if (time < 1000) {\n            return \"\".concat(time, \"ms\");\n        } else {\n            return \"\".concat((time / 1000).toFixed(1), \"s\");\n        }\n    };\n    const getPerformanceMessage = (time)=>{\n        if (!time || isNaN(time)) return \"\";\n        if (time < 500) return \"Blazing fast!\";\n        if (time < 1000) return \"Lightning quick!\";\n        if (time < 2000) return \"Pretty speedy!\";\n        return \"Getting there!\";\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"inline-flex items-center space-x-2 \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-2 h-2 bg-current rounded-full animate-pulse\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm font-mono\",\n                    children: \"Measuring...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n            lineNumber: 109,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"inline-flex items-center space-x-2 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-2 h-2 bg-current rounded-full\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                lineNumber: 118,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm font-mono\",\n                children: [\n                    \"This website loaded in \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: [\n                            \"~\",\n                            formatLoadTime(loadTime)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 32\n                    }, this),\n                    loadTime && !isNaN(loadTime) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"ml-2 opacity-80\",\n                        children: getPerformanceMessage(loadTime)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n        lineNumber: 117,\n        columnNumber: 5\n    }, this);\n}\n_s(LoadTimeDisplay, \"Wk6++ihLZAXguEzIXEKQHPxN2hU=\");\n_c = LoadTimeDisplay;\nvar _c;\n$RefreshReg$(_c, \"LoadTimeDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/LoadTimeDisplay.tsx\n"));

/***/ })

});