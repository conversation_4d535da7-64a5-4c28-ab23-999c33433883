"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ui/LoadTimeDisplay.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/LoadTimeDisplay.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LoadTimeDisplay; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction LoadTimeDisplay(param) {\n    let { className = \"\" } = param;\n    _s();\n    const [metrics, setMetrics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        domProcessing: null,\n        networkTime: null,\n        totalLoad: null\n    });\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const measurePerformance = ()=>{\n            try {\n                const navigation = performance.getEntriesByType(\"navigation\")[0];\n                if (navigation) {\n                    const newMetrics = {\n                        domProcessing: null,\n                        networkTime: null,\n                        totalLoad: null\n                    };\n                    // DOM Processing Time (website optimization)\n                    if (navigation.domContentLoadedEventEnd > 0 && navigation.responseEnd > 0) {\n                        newMetrics.domProcessing = Math.round(navigation.domContentLoadedEventEnd - navigation.responseEnd);\n                    }\n                    // Network Time (connection quality)\n                    if (navigation.responseEnd > 0 && navigation.fetchStart >= 0) {\n                        newMetrics.networkTime = Math.round(navigation.responseEnd - navigation.fetchStart);\n                    }\n                    // Total Load Time (overall experience)\n                    if (navigation.loadEventStart > 0 && navigation.fetchStart >= 0) {\n                        newMetrics.totalLoad = Math.round(navigation.loadEventStart - navigation.fetchStart);\n                    }\n                    console.log(\"Performance metrics:\", newMetrics);\n                    setMetrics(newMetrics);\n                    setIsLoading(false);\n                } else {\n                    // Fallback metrics\n                    setMetrics({\n                        domProcessing: 50,\n                        networkTime: 150,\n                        totalLoad: 750\n                    });\n                    setIsLoading(false);\n                }\n            } catch (error) {\n                console.warn(\"Error measuring performance:\", error);\n                setMetrics({\n                    domProcessing: 50,\n                    networkTime: 150,\n                    totalLoad: 750\n                });\n                setIsLoading(false);\n            }\n        };\n        // Strategy: Wait for everything to be ready\n        const initMeasurement = ()=>{\n            if (document.readyState === \"complete\") {\n                // Page is already loaded, measure immediately with a small delay\n                setTimeout(measurePerformance, 200);\n            } else {\n                // Wait for the load event\n                const handleLoad = ()=>{\n                    setTimeout(measurePerformance, 200);\n                };\n                window.addEventListener(\"load\", handleLoad, {\n                    once: true\n                });\n                return ()=>window.removeEventListener(\"load\", handleLoad);\n            }\n        };\n        initMeasurement();\n    }, []);\n    const formatTime = (time)=>{\n        if (!time || isNaN(time)) return \"N/A\";\n        if (time < 1000) {\n            return \"\".concat(time, \"ms\");\n        } else {\n            return \"\".concat((time / 1000).toFixed(1), \"s\");\n        }\n    };\n    const getMetricStatus = (metric, time)=>{\n        if (!time || isNaN(time)) return {\n            status: \"unknown\",\n            message: \"\",\n            note: \"\"\n        };\n        switch(metric){\n            case \"dom\":\n                if (time < 50) return {\n                    status: \"excellent\",\n                    message: \"Blazing fast!\",\n                    note: \"\"\n                };\n                if (time < 100) return {\n                    status: \"good\",\n                    message: \"Lightning quick!\",\n                    note: \"\"\n                };\n                if (time < 200) return {\n                    status: \"fair\",\n                    message: \"Pretty good!\",\n                    note: \"\"\n                };\n                return {\n                    status: \"slow\",\n                    message: \"Could be faster\",\n                    note: \"Website optimization needed\"\n                };\n            case \"network\":\n                if (time < 100) return {\n                    status: \"excellent\",\n                    message: \"Excellent connection!\",\n                    note: \"\"\n                };\n                if (time < 300) return {\n                    status: \"good\",\n                    message: \"Good connection\",\n                    note: \"\"\n                };\n                if (time < 1000) return {\n                    status: \"fair\",\n                    message: \"Moderate connection\",\n                    note: \"\"\n                };\n                return {\n                    status: \"slow\",\n                    message: \"Slow connection\",\n                    note: \"Check your internet connection\"\n                };\n            case \"total\":\n                if (time < 500) return {\n                    status: \"excellent\",\n                    message: \"Blazing fast!\",\n                    note: \"\"\n                };\n                if (time < 1000) return {\n                    status: \"good\",\n                    message: \"Lightning quick!\",\n                    note: \"\"\n                };\n                if (time < 2000) return {\n                    status: \"fair\",\n                    message: \"Pretty speedy!\",\n                    note: \"\"\n                };\n                return {\n                    status: \"slow\",\n                    message: \"Taking a while\",\n                    note: \"Is your internet connection stable?\"\n                };\n            default:\n                return {\n                    status: \"unknown\",\n                    message: \"\",\n                    note: \"\"\n                };\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"excellent\":\n                return \"text-green-400\";\n            case \"good\":\n                return \"text-bauhaus-yellow\";\n            case \"fair\":\n                return \"text-orange-400\";\n            case \"slow\":\n                return \"text-red-400\";\n            default:\n                return \"text-gray-400\";\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"inline-flex items-center space-x-2 \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-2 h-2 bg-current rounded-full animate-pulse\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm font-mono\",\n                    children: \"Measuring...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n            lineNumber: 139,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"inline-flex items-center space-x-2 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-2 h-2 bg-current rounded-full\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                lineNumber: 148,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm font-mono\",\n                children: [\n                    getDisplayText(loadTime, isDomProcessing),\n                    loadTime && !isNaN(loadTime) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"ml-2 opacity-80\",\n                        children: getPerformanceMessage(loadTime, isDomProcessing)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                lineNumber: 149,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n        lineNumber: 147,\n        columnNumber: 5\n    }, this);\n}\n_s(LoadTimeDisplay, \"6FitVEwf91HySuhJ+VaOEnPX+Os=\");\n_c = LoadTimeDisplay;\nvar _c;\n$RefreshReg$(_c, \"LoadTimeDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/LoadTimeDisplay.tsx\n"));

/***/ })

});