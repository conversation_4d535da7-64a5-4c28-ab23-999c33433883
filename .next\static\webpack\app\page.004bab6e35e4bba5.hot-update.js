"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ui/LoadTimeDisplay.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/LoadTimeDisplay.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LoadTimeDisplay; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction LoadTimeDisplay(param) {\n    let { className = \"\" } = param;\n    _s();\n    const [loadTime, setLoadTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const measureLoadTime = ()=>{\n            try {\n                const navigation = performance.getEntriesByType(\"navigation\")[0];\n                if (navigation) {\n                    // The Network tab's \"Load\" time is loadEventStart from fetchStart\n                    // This matches exactly what the browser dev tools show\n                    if (navigation.loadEventStart > 0 && navigation.fetchStart >= 0) {\n                        const loadTime = navigation.loadEventStart - navigation.fetchStart;\n                        console.log(\"Load time (matches Network tab):\", Math.round(loadTime));\n                        if (loadTime > 0 && loadTime < 60000) {\n                            setLoadTime(Math.round(loadTime));\n                            setIsLoading(false);\n                            return;\n                        }\n                    }\n                    // Fallback: DOMContentLoaded time (also useful metric)\n                    if (navigation.domContentLoadedEventEnd > 0 && navigation.fetchStart >= 0) {\n                        const domTime = navigation.domContentLoadedEventEnd - navigation.fetchStart;\n                        console.log(\"DOMContentLoaded time:\", Math.round(domTime));\n                        if (domTime > 0 && domTime < 60000) {\n                            setLoadTime(Math.round(domTime));\n                            setIsLoading(false);\n                            return;\n                        }\n                    }\n                    // Final fallback: response time\n                    if (navigation.responseEnd > 0 && navigation.fetchStart >= 0) {\n                        const responseTime = navigation.responseEnd - navigation.fetchStart;\n                        console.log(\"Response time:\", Math.round(responseTime));\n                        if (responseTime > 0 && responseTime < 60000) {\n                            setLoadTime(Math.round(responseTime));\n                            setIsLoading(false);\n                            return;\n                        }\n                    }\n                }\n                // Ultimate fallback\n                console.log(\"Using default timing\");\n                setLoadTime(750);\n                setIsLoading(false);\n            } catch (error) {\n                console.warn(\"Error measuring load time:\", error);\n                setLoadTime(750);\n                setIsLoading(false);\n            }\n        };\n        // Strategy: Wait for everything to be ready\n        const initMeasurement = ()=>{\n            if (document.readyState === \"complete\") {\n                // Page is already loaded, measure immediately with a small delay\n                setTimeout(measureLoadTime, 200);\n            } else {\n                // Wait for the load event\n                const handleLoad = ()=>{\n                    setTimeout(measureLoadTime, 200);\n                };\n                window.addEventListener(\"load\", handleLoad, {\n                    once: true\n                });\n                return ()=>window.removeEventListener(\"load\", handleLoad);\n            }\n        };\n        initMeasurement();\n    }, []);\n    const formatLoadTime = (time)=>{\n        if (!time || isNaN(time)) return \"N/A\";\n        if (time < 1000) {\n            return \"\".concat(time, \"ms\");\n        } else {\n            return \"\".concat((time / 1000).toFixed(1), \"s\");\n        }\n    };\n    const getPerformanceMessage = (time)=>{\n        if (!time || isNaN(time)) return \"\";\n        if (time < 500) return \"Blazing fast!\";\n        if (time < 1000) return \"Lightning quick!\";\n        if (time < 2000) return \"Pretty speedy!\";\n        return \"Getting there!\";\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"inline-flex items-center space-x-2 \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-2 h-2 bg-current rounded-full animate-pulse\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm font-mono\",\n                    children: \"Measuring...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n            lineNumber: 104,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"inline-flex items-center space-x-2 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-2 h-2 bg-current rounded-full\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm font-mono\",\n                children: [\n                    \"This website loaded in \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: formatLoadTime(loadTime)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 32\n                    }, this),\n                    loadTime && !isNaN(loadTime) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"ml-2 opacity-80\",\n                        children: getPerformanceMessage(loadTime)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                lineNumber: 114,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n        lineNumber: 112,\n        columnNumber: 5\n    }, this);\n}\n_s(LoadTimeDisplay, \"Wk6++ihLZAXguEzIXEKQHPxN2hU=\");\n_c = LoadTimeDisplay;\nvar _c;\n$RefreshReg$(_c, \"LoadTimeDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/LoadTimeDisplay.tsx\n"));

/***/ })

});