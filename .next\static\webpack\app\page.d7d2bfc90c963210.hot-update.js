"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ui/LoadTimeDisplay.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/LoadTimeDisplay.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LoadTimeDisplay; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction LoadTimeDisplay(param) {\n    let { className = \"\" } = param;\n    _s();\n    const [loadTime, setLoadTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const measureLoadTime = ()=>{\n            try {\n                const navigation = performance.getEntriesByType(\"navigation\")[0];\n                if (navigation) {\n                    // Option 1: DOM Processing Time (website performance, not network)\n                    // This measures how fast your website processes after HTML arrives\n                    if (navigation.domContentLoadedEventEnd > 0 && navigation.responseEnd > 0) {\n                        const domProcessingTime = navigation.domContentLoadedEventEnd - navigation.responseEnd;\n                        console.log(\"DOM processing time: \".concat(Math.round(domProcessingTime), \"ms (website performance)\"));\n                        if (domProcessingTime > 0 && domProcessingTime < 10000) {\n                            setLoadTime(Math.round(domProcessingTime));\n                            setIsLoading(false);\n                            return;\n                        }\n                    }\n                    // Option 2: Full Load Time (includes network - current approach)\n                    if (navigation.loadEventStart > 0 && navigation.fetchStart >= 0) {\n                        const loadTime = navigation.loadEventStart - navigation.fetchStart;\n                        console.log(\"Full load time: \".concat(Math.round(loadTime), \"ms (includes network)\"));\n                        setLoadTime(Math.round(loadTime));\n                        setIsLoading(false);\n                        return;\n                    }\n                    // Fallback: DOMContentLoaded timing\n                    if (navigation.domContentLoadedEventEnd > 0 && navigation.fetchStart >= 0) {\n                        const domTime = navigation.domContentLoadedEventEnd - navigation.fetchStart;\n                        console.log(\"DOMContentLoaded time: \".concat(Math.round(domTime), \"ms\"));\n                        setLoadTime(Math.round(domTime));\n                        setIsLoading(false);\n                        return;\n                    }\n                    // Final fallback: Response time\n                    if (navigation.responseEnd > 0 && navigation.fetchStart >= 0) {\n                        const responseTime = navigation.responseEnd - navigation.fetchStart;\n                        console.log(\"Response time: \".concat(Math.round(responseTime), \"ms\"));\n                        setLoadTime(Math.round(responseTime));\n                        setIsLoading(false);\n                        return;\n                    }\n                }\n                // Ultimate fallback\n                console.log(\"Using default timing\");\n                setLoadTime(750);\n                setIsLoading(false);\n            } catch (error) {\n                console.warn(\"Error measuring load time:\", error);\n                setLoadTime(750);\n                setIsLoading(false);\n            }\n        };\n        // Strategy: Wait for everything to be ready\n        const initMeasurement = ()=>{\n            if (document.readyState === \"complete\") {\n                // Page is already loaded, measure immediately with a small delay\n                setTimeout(measureLoadTime, 200);\n            } else {\n                // Wait for the load event\n                const handleLoad = ()=>{\n                    setTimeout(measureLoadTime, 200);\n                };\n                window.addEventListener(\"load\", handleLoad, {\n                    once: true\n                });\n                return ()=>window.removeEventListener(\"load\", handleLoad);\n            }\n        };\n        initMeasurement();\n    }, []);\n    const formatLoadTime = (time)=>{\n        if (!time || isNaN(time)) return \"N/A\";\n        if (time < 1000) {\n            return \"\".concat(time, \"ms\");\n        } else {\n            return \"\".concat((time / 1000).toFixed(1), \"s\");\n        }\n    };\n    const getPerformanceMessage = function(time) {\n        let isDomProcessing = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (!time || isNaN(time)) return \"\";\n        if (isDomProcessing) {\n            // DOM processing time thresholds (much lower)\n            if (time < 50) return \"Blazing fast!\";\n            if (time < 100) return \"Lightning quick!\";\n            if (time < 200) return \"Pretty speedy!\";\n            return \"Getting there!\";\n        } else {\n            // Full load time thresholds\n            if (time < 500) return \"Blazing fast!\";\n            if (time < 1000) return \"Lightning quick!\";\n            if (time < 2000) return \"Pretty speedy!\";\n            return \"Getting there!\";\n        }\n    };\n    const getDisplayText = function(time) {\n        let isDomProcessing = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (!time || isNaN(time)) return \"This website loaded in N/A\";\n        if (isDomProcessing && time < 200) {\n            // For fast DOM processing, show it as \"optimized performance\"\n            return \"This optimized website performs in ~\".concat(formatLoadTime(time));\n        } else {\n            // For everything else, show as load time\n            return \"This website loaded in ~\".concat(formatLoadTime(time));\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"inline-flex items-center space-x-2 \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-2 h-2 bg-current rounded-full animate-pulse\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm font-mono\",\n                    children: \"Measuring...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n            lineNumber: 130,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"inline-flex items-center space-x-2 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-2 h-2 bg-current rounded-full\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                lineNumber: 139,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm font-mono\",\n                children: [\n                    \"DOM processed in \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: [\n                            \"~\",\n                            formatLoadTime(loadTime)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 26\n                    }, this),\n                    loadTime && !isNaN(loadTime) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"ml-2 opacity-80\",\n                        children: getPerformanceMessage(loadTime)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n        lineNumber: 138,\n        columnNumber: 5\n    }, this);\n}\n_s(LoadTimeDisplay, \"Wk6++ihLZAXguEzIXEKQHPxN2hU=\");\n_c = LoadTimeDisplay;\nvar _c;\n$RefreshReg$(_c, \"LoadTimeDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/LoadTimeDisplay.tsx\n"));

/***/ })

});