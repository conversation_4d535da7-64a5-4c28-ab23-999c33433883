"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ui/LoadTimeDisplay.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/LoadTimeDisplay.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LoadTimeDisplay; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction LoadTimeDisplay(param) {\n    let { className = \"\" } = param;\n    _s();\n    const [loadTime, setLoadTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const measureLoadTime = ()=>{\n            try {\n                const navigation = performance.getEntriesByType(\"navigation\")[0];\n                if (navigation) {\n                    // Log all the timing values to see what we have\n                    console.log(\"All navigation timings:\", {\n                        navigationStart: navigation.navigationStart,\n                        fetchStart: navigation.fetchStart,\n                        domainLookupStart: navigation.domainLookupStart,\n                        domainLookupEnd: navigation.domainLookupEnd,\n                        connectStart: navigation.connectStart,\n                        connectEnd: navigation.connectEnd,\n                        requestStart: navigation.requestStart,\n                        responseStart: navigation.responseStart,\n                        responseEnd: navigation.responseEnd,\n                        domLoading: navigation.domLoading,\n                        domInteractive: navigation.domInteractive,\n                        domContentLoadedEventStart: navigation.domContentLoadedEventStart,\n                        domContentLoadedEventEnd: navigation.domContentLoadedEventEnd,\n                        domComplete: navigation.domComplete,\n                        loadEventStart: navigation.loadEventStart,\n                        loadEventEnd: navigation.loadEventEnd\n                    });\n                    // Try to match the dev console's \"Load\" time\n                    // This is typically domContentLoadedEventEnd - fetchStart or navigationStart\n                    if (navigation.domContentLoadedEventEnd > 0 && navigation.fetchStart > 0) {\n                        const loadTime = navigation.domContentLoadedEventEnd - navigation.fetchStart;\n                        console.log(\"DOMContentLoaded from fetchStart:\", loadTime);\n                        if (loadTime > 0 && loadTime < 60000) {\n                            setLoadTime(Math.round(loadTime));\n                            setIsLoading(false);\n                            return;\n                        }\n                    }\n                    // Alternative: try responseEnd - fetchStart (network time)\n                    if (navigation.responseEnd > 0 && navigation.fetchStart > 0) {\n                        const networkTime = navigation.responseEnd - navigation.fetchStart;\n                        console.log(\"Network time (responseEnd - fetchStart):\", networkTime);\n                        if (networkTime > 0 && networkTime < 60000) {\n                            setLoadTime(Math.round(networkTime));\n                            setIsLoading(false);\n                            return;\n                        }\n                    }\n                    // Fallback to DOMContentLoaded from navigationStart\n                    if (navigation.domContentLoadedEventEnd > 0 && navigation.navigationStart > 0) {\n                        const domLoadTime = navigation.domContentLoadedEventEnd - navigation.navigationStart;\n                        console.log(\"DOMContentLoaded from navigationStart:\", domLoadTime);\n                        if (domLoadTime > 0 && domLoadTime < 60000) {\n                            setLoadTime(Math.round(domLoadTime));\n                            setIsLoading(false);\n                            return;\n                        }\n                    }\n                }\n                // Final fallback\n                console.log(\"Using default timing\");\n                setLoadTime(750);\n                setIsLoading(false);\n            } catch (error) {\n                console.warn(\"Error measuring load time:\", error);\n                setLoadTime(750);\n                setIsLoading(false);\n            }\n        };\n        // Strategy: Wait for everything to be ready\n        const initMeasurement = ()=>{\n            if (document.readyState === \"complete\") {\n                // Page is already loaded, measure immediately with a small delay\n                setTimeout(measureLoadTime, 200);\n            } else {\n                // Wait for the load event\n                const handleLoad = ()=>{\n                    setTimeout(measureLoadTime, 200);\n                };\n                window.addEventListener(\"load\", handleLoad, {\n                    once: true\n                });\n                return ()=>window.removeEventListener(\"load\", handleLoad);\n            }\n        };\n        initMeasurement();\n    }, []);\n    const formatLoadTime = (time)=>{\n        if (!time || isNaN(time)) return \"N/A\";\n        if (time < 1000) {\n            return \"\".concat(time, \"ms\");\n        } else {\n            return \"\".concat((time / 1000).toFixed(1), \"s\");\n        }\n    };\n    const getPerformanceMessage = (time)=>{\n        if (!time || isNaN(time)) return \"\";\n        if (time < 500) return \"Blazing fast!\";\n        if (time < 1000) return \"Lightning quick!\";\n        if (time < 2000) return \"Pretty speedy!\";\n        return \"Getting there!\";\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"inline-flex items-center space-x-2 \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-2 h-2 bg-current rounded-full animate-pulse\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm font-mono\",\n                    children: \"Measuring...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n            lineNumber: 124,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"inline-flex items-center space-x-2 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-2 h-2 bg-current rounded-full\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm font-mono\",\n                children: [\n                    \"This website loaded in \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: formatLoadTime(loadTime)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 32\n                    }, this),\n                    loadTime && !isNaN(loadTime) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"ml-2 opacity-80\",\n                        children: getPerformanceMessage(loadTime)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n        lineNumber: 132,\n        columnNumber: 5\n    }, this);\n}\n_s(LoadTimeDisplay, \"Wk6++ihLZAXguEzIXEKQHPxN2hU=\");\n_c = LoadTimeDisplay;\nvar _c;\n$RefreshReg$(_c, \"LoadTimeDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/LoadTimeDisplay.tsx\n"));

/***/ })

});