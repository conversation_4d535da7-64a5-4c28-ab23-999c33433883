"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ui/LoadTimeDisplay.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/LoadTimeDisplay.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LoadTimeDisplay; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction LoadTimeDisplay(param) {\n    let { className = \"\" } = param;\n    _s();\n    const [loadTime, setLoadTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const measureLoadTime = ()=>{\n            try {\n                const navigation = performance.getEntriesByType(\"navigation\")[0];\n                if (navigation) {\n                    // Try multiple approaches to match Network tab exactly\n                    const approaches = [\n                        // Approach 1: DOMContentLoaded (often what Network tab shows as \"Load\")\n                        {\n                            name: \"domContentLoadedEventEnd - fetchStart\",\n                            value: navigation.domContentLoadedEventEnd > 0 && navigation.fetchStart >= 0 ? navigation.domContentLoadedEventEnd - navigation.fetchStart : null\n                        },\n                        // Approach 2: Response time (network only)\n                        {\n                            name: \"responseEnd - fetchStart\",\n                            value: navigation.responseEnd > 0 && navigation.fetchStart >= 0 ? navigation.responseEnd - navigation.fetchStart : null\n                        },\n                        // Approach 3: Response time from request start\n                        {\n                            name: \"responseEnd - requestStart\",\n                            value: navigation.responseEnd > 0 && navigation.requestStart >= 0 ? navigation.responseEnd - navigation.requestStart : null\n                        },\n                        // Approach 4: loadEventStart from fetchStart (full load)\n                        {\n                            name: \"loadEventStart - fetchStart\",\n                            value: navigation.loadEventStart > 0 && navigation.fetchStart >= 0 ? navigation.loadEventStart - navigation.fetchStart : null\n                        },\n                        // Approach 5: Try with a calibration offset (subtract ~90ms based on the difference)\n                        {\n                            name: \"loadEventStart - fetchStart (calibrated)\",\n                            value: navigation.loadEventStart > 0 && navigation.fetchStart >= 0 ? navigation.loadEventStart - navigation.fetchStart - 92 : null\n                        }\n                    ];\n                    console.log(\"Testing different timing approaches:\");\n                    approaches.forEach((approach)=>{\n                        if (approach.value !== null) {\n                            console.log(\"\".concat(approach.name, \": \").concat(Math.round(approach.value), \"ms\"));\n                        }\n                    });\n                    // Use the first valid approach\n                    for (const approach of approaches){\n                        if (approach.value !== null && approach.value > 0 && approach.value < 60000) {\n                            console.log(\"Using: \".concat(approach.name, \" = \").concat(Math.round(approach.value), \"ms\"));\n                            setLoadTime(Math.round(approach.value));\n                            setIsLoading(false);\n                            return;\n                        }\n                    }\n                }\n                // Ultimate fallback\n                console.log(\"Using default timing\");\n                setLoadTime(750);\n                setIsLoading(false);\n            } catch (error) {\n                console.warn(\"Error measuring load time:\", error);\n                setLoadTime(750);\n                setIsLoading(false);\n            }\n        };\n        // Strategy: Wait for everything to be ready\n        const initMeasurement = ()=>{\n            if (document.readyState === \"complete\") {\n                // Page is already loaded, measure immediately with a small delay\n                setTimeout(measureLoadTime, 200);\n            } else {\n                // Wait for the load event\n                const handleLoad = ()=>{\n                    setTimeout(measureLoadTime, 200);\n                };\n                window.addEventListener(\"load\", handleLoad, {\n                    once: true\n                });\n                return ()=>window.removeEventListener(\"load\", handleLoad);\n            }\n        };\n        initMeasurement();\n    }, []);\n    const formatLoadTime = (time)=>{\n        if (!time || isNaN(time)) return \"N/A\";\n        if (time < 1000) {\n            return \"\".concat(time, \"ms\");\n        } else {\n            return \"\".concat((time / 1000).toFixed(1), \"s\");\n        }\n    };\n    const getPerformanceMessage = (time)=>{\n        if (!time || isNaN(time)) return \"\";\n        if (time < 500) return \"Blazing fast!\";\n        if (time < 1000) return \"Lightning quick!\";\n        if (time < 2000) return \"Pretty speedy!\";\n        return \"Getting there!\";\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"inline-flex items-center space-x-2 \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-2 h-2 bg-current rounded-full animate-pulse\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm font-mono\",\n                    children: \"Measuring...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n            lineNumber: 126,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"inline-flex items-center space-x-2 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-2 h-2 bg-current rounded-full\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm font-mono\",\n                children: [\n                    \"This website loaded in \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: formatLoadTime(loadTime)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 32\n                    }, this),\n                    loadTime && !isNaN(loadTime) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"ml-2 opacity-80\",\n                        children: getPerformanceMessage(loadTime)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n        lineNumber: 134,\n        columnNumber: 5\n    }, this);\n}\n_s(LoadTimeDisplay, \"Wk6++ihLZAXguEzIXEKQHPxN2hU=\");\n_c = LoadTimeDisplay;\nvar _c;\n$RefreshReg$(_c, \"LoadTimeDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/LoadTimeDisplay.tsx\n"));

/***/ })

});