"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ui/LoadTimeDisplay.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/LoadTimeDisplay.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LoadTimeDisplay; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction LoadTimeDisplay(param) {\n    let { className = \"\" } = param;\n    _s();\n    const [loadTime, setLoadTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const measureLoadTime = ()=>{\n            try {\n                const navigation = performance.getEntriesByType(\"navigation\")[0];\n                if (navigation) {\n                    // Try multiple approaches to match Network tab exactly\n                    const approaches = [\n                        // Approach 1: loadEventStart from fetchStart (most likely)\n                        {\n                            name: \"loadEventStart - fetchStart\",\n                            value: navigation.loadEventStart > 0 && navigation.fetchStart >= 0 ? navigation.loadEventStart - navigation.fetchStart : null\n                        },\n                        // Approach 2: loadEventStart from navigationStart (alternative base)\n                        {\n                            name: \"loadEventStart - navigationStart\",\n                            value: navigation.loadEventStart > 0 && navigation.navigationStart >= 0 ? navigation.loadEventStart - navigation.navigationStart : null\n                        },\n                        // Approach 3: loadEventStart from requestStart\n                        {\n                            name: \"loadEventStart - requestStart\",\n                            value: navigation.loadEventStart > 0 && navigation.requestStart >= 0 ? navigation.loadEventStart - navigation.requestStart : null\n                        },\n                        // Approach 4: Just loadEventStart (absolute time)\n                        {\n                            name: \"loadEventStart (absolute)\",\n                            value: navigation.loadEventStart > 0 ? navigation.loadEventStart : null\n                        }\n                    ];\n                    console.log(\"Testing different timing approaches:\");\n                    approaches.forEach((approach)=>{\n                        if (approach.value !== null) {\n                            console.log(\"\".concat(approach.name, \": \").concat(Math.round(approach.value), \"ms\"));\n                        }\n                    });\n                    // Use the first valid approach\n                    for (const approach of approaches){\n                        if (approach.value !== null && approach.value > 0 && approach.value < 60000) {\n                            console.log(\"Using: \".concat(approach.name, \" = \").concat(Math.round(approach.value), \"ms\"));\n                            setLoadTime(Math.round(approach.value));\n                            setIsLoading(false);\n                            return;\n                        }\n                    }\n                }\n                // Ultimate fallback\n                console.log(\"Using default timing\");\n                setLoadTime(750);\n                setIsLoading(false);\n            } catch (error) {\n                console.warn(\"Error measuring load time:\", error);\n                setLoadTime(750);\n                setIsLoading(false);\n            }\n        };\n        // Strategy: Wait for everything to be ready\n        const initMeasurement = ()=>{\n            if (document.readyState === \"complete\") {\n                // Page is already loaded, measure immediately with a small delay\n                setTimeout(measureLoadTime, 200);\n            } else {\n                // Wait for the load event\n                const handleLoad = ()=>{\n                    setTimeout(measureLoadTime, 200);\n                };\n                window.addEventListener(\"load\", handleLoad, {\n                    once: true\n                });\n                return ()=>window.removeEventListener(\"load\", handleLoad);\n            }\n        };\n        initMeasurement();\n    }, []);\n    const formatLoadTime = (time)=>{\n        if (!time || isNaN(time)) return \"N/A\";\n        if (time < 1000) {\n            return \"\".concat(time, \"ms\");\n        } else {\n            return \"\".concat((time / 1000).toFixed(1), \"s\");\n        }\n    };\n    const getPerformanceMessage = (time)=>{\n        if (!time || isNaN(time)) return \"\";\n        if (time < 500) return \"Blazing fast!\";\n        if (time < 1000) return \"Lightning quick!\";\n        if (time < 2000) return \"Pretty speedy!\";\n        return \"Getting there!\";\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"inline-flex items-center space-x-2 \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-2 h-2 bg-current rounded-full animate-pulse\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm font-mono\",\n                    children: \"Measuring...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n            lineNumber: 117,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"inline-flex items-center space-x-2 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-2 h-2 bg-current rounded-full\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm font-mono\",\n                children: [\n                    \"This website loaded in \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: formatLoadTime(loadTime)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 32\n                    }, this),\n                    loadTime && !isNaN(loadTime) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"ml-2 opacity-80\",\n                        children: getPerformanceMessage(loadTime)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n        lineNumber: 125,\n        columnNumber: 5\n    }, this);\n}\n_s(LoadTimeDisplay, \"Wk6++ihLZAXguEzIXEKQHPxN2hU=\");\n_c = LoadTimeDisplay;\nvar _c;\n$RefreshReg$(_c, \"LoadTimeDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/LoadTimeDisplay.tsx\n"));

/***/ })

});