"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ui/LoadTimeDisplay.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/LoadTimeDisplay.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LoadTimeDisplay; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction LoadTimeDisplay(param) {\n    let { className = \"\" } = param;\n    _s();\n    const [loadTime, setLoadTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isDomProcessing, setIsDomProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const measureLoadTime = ()=>{\n            try {\n                const navigation = performance.getEntriesByType(\"navigation\")[0];\n                if (navigation) {\n                    // Strategy: Use DOM processing if it's impressively fast, otherwise use full load time\n                    // Check DOM processing time first\n                    if (navigation.domContentLoadedEventEnd > 0 && navigation.responseEnd > 0) {\n                        const domProcessingTime = navigation.domContentLoadedEventEnd - navigation.responseEnd;\n                        console.log(\"DOM processing time: \".concat(Math.round(domProcessingTime), \"ms\"));\n                        // If DOM processing is impressively fast (< 200ms), showcase that\n                        if (domProcessingTime > 0 && domProcessingTime < 200) {\n                            console.log(\"Using DOM processing time (impressive performance)\");\n                            setLoadTime(Math.round(domProcessingTime));\n                            setIsDomProcessing(true);\n                            setIsLoading(false);\n                            return;\n                        }\n                    }\n                    // Otherwise, use full load time (more relatable to users)\n                    if (navigation.loadEventStart > 0 && navigation.fetchStart >= 0) {\n                        const fullLoadTime = navigation.loadEventStart - navigation.fetchStart;\n                        console.log(\"Full load time: \".concat(Math.round(fullLoadTime), \"ms\"));\n                        setLoadTime(Math.round(fullLoadTime));\n                        setIsDomProcessing(false);\n                        setIsLoading(false);\n                        return;\n                    }\n                    // Fallback: DOMContentLoaded timing\n                    if (navigation.domContentLoadedEventEnd > 0 && navigation.fetchStart >= 0) {\n                        const domTime = navigation.domContentLoadedEventEnd - navigation.fetchStart;\n                        console.log(\"DOMContentLoaded time: \".concat(Math.round(domTime), \"ms\"));\n                        setLoadTime(Math.round(domTime));\n                        setIsLoading(false);\n                        return;\n                    }\n                    // Final fallback: Response time\n                    if (navigation.responseEnd > 0 && navigation.fetchStart >= 0) {\n                        const responseTime = navigation.responseEnd - navigation.fetchStart;\n                        console.log(\"Response time: \".concat(Math.round(responseTime), \"ms\"));\n                        setLoadTime(Math.round(responseTime));\n                        setIsLoading(false);\n                        return;\n                    }\n                }\n                // Ultimate fallback\n                console.log(\"Using default timing\");\n                setLoadTime(750);\n                setIsLoading(false);\n            } catch (error) {\n                console.warn(\"Error measuring load time:\", error);\n                setLoadTime(750);\n                setIsLoading(false);\n            }\n        };\n        // Strategy: Wait for everything to be ready\n        const initMeasurement = ()=>{\n            if (document.readyState === \"complete\") {\n                // Page is already loaded, measure immediately with a small delay\n                setTimeout(measureLoadTime, 200);\n            } else {\n                // Wait for the load event\n                const handleLoad = ()=>{\n                    setTimeout(measureLoadTime, 200);\n                };\n                window.addEventListener(\"load\", handleLoad, {\n                    once: true\n                });\n                return ()=>window.removeEventListener(\"load\", handleLoad);\n            }\n        };\n        initMeasurement();\n    }, []);\n    const formatLoadTime = (time)=>{\n        if (!time || isNaN(time)) return \"N/A\";\n        if (time < 1000) {\n            return \"\".concat(time, \"ms\");\n        } else {\n            return \"\".concat((time / 1000).toFixed(1), \"s\");\n        }\n    };\n    const getPerformanceMessage = function(time) {\n        let isDomProcessing = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (!time || isNaN(time)) return \"\";\n        if (isDomProcessing) {\n            // DOM processing time thresholds (much lower)\n            if (time < 50) return \"Blazing fast!\";\n            if (time < 100) return \"Lightning quick!\";\n            if (time < 200) return \"Pretty speedy!\";\n            return \"Getting there!\";\n        } else {\n            // Full load time thresholds\n            if (time < 500) return \"Blazing fast!\";\n            if (time < 1000) return \"Lightning quick!\";\n            if (time < 2000) return \"Pretty speedy!\";\n            return \"Getting there!\";\n        }\n    };\n    const getDisplayText = function(time) {\n        let isDomProcessing = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (!time || isNaN(time)) return \"This website loaded in N/A\";\n        if (isDomProcessing && time < 200) {\n            // For fast DOM processing, show it as \"optimized performance\"\n            return \"This optimized website performs in ~\".concat(formatLoadTime(time));\n        } else {\n            // For everything else, show as load time\n            return \"This website loaded in ~\".concat(formatLoadTime(time));\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"inline-flex items-center space-x-2 \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-2 h-2 bg-current rounded-full animate-pulse\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm font-mono\",\n                    children: \"Measuring...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n            lineNumber: 137,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"inline-flex items-center space-x-2 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-2 h-2 bg-current rounded-full\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                lineNumber: 146,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm font-mono\",\n                children: [\n                    getDisplayText(loadTime, isDomProcessing),\n                    loadTime && !isNaN(loadTime) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"ml-2 opacity-80\",\n                        children: getPerformanceMessage(loadTime, isDomProcessing)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                lineNumber: 147,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n        lineNumber: 145,\n        columnNumber: 5\n    }, this);\n}\n_s(LoadTimeDisplay, \"33yJbvgRUMyQorwGi4O5PbhnLNY=\");\n_c = LoadTimeDisplay;\nvar _c;\n$RefreshReg$(_c, \"LoadTimeDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/LoadTimeDisplay.tsx\n"));

/***/ })

});