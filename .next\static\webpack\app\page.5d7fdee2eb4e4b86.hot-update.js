"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ui/LoadTimeDisplay.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/LoadTimeDisplay.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LoadTimeDisplay; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction LoadTimeDisplay(param) {\n    let { className = \"\" } = param;\n    _s();\n    const [loadTime, setLoadTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isDomProcessing, setIsDomProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const measureLoadTime = ()=>{\n            try {\n                const navigation = performance.getEntriesByType(\"navigation\")[0];\n                if (navigation) {\n                    // Option 1: DOM Processing Time (website performance, not network)\n                    // This measures how fast your website processes after HTML arrives\n                    if (navigation.domContentLoadedEventEnd > 0 && navigation.responseEnd > 0) {\n                        const domProcessingTime = navigation.domContentLoadedEventEnd - navigation.responseEnd;\n                        console.log(\"DOM processing time: \".concat(Math.round(domProcessingTime), \"ms (website performance)\"));\n                        if (domProcessingTime > 0 && domProcessingTime < 10000) {\n                            setLoadTime(Math.round(domProcessingTime));\n                            setIsLoading(false);\n                            return;\n                        }\n                    }\n                    // Option 2: Full Load Time (includes network - current approach)\n                    if (navigation.loadEventStart > 0 && navigation.fetchStart >= 0) {\n                        const loadTime = navigation.loadEventStart - navigation.fetchStart;\n                        console.log(\"Full load time: \".concat(Math.round(loadTime), \"ms (includes network)\"));\n                        setLoadTime(Math.round(loadTime));\n                        setIsLoading(false);\n                        return;\n                    }\n                    // Fallback: DOMContentLoaded timing\n                    if (navigation.domContentLoadedEventEnd > 0 && navigation.fetchStart >= 0) {\n                        const domTime = navigation.domContentLoadedEventEnd - navigation.fetchStart;\n                        console.log(\"DOMContentLoaded time: \".concat(Math.round(domTime), \"ms\"));\n                        setLoadTime(Math.round(domTime));\n                        setIsLoading(false);\n                        return;\n                    }\n                    // Final fallback: Response time\n                    if (navigation.responseEnd > 0 && navigation.fetchStart >= 0) {\n                        const responseTime = navigation.responseEnd - navigation.fetchStart;\n                        console.log(\"Response time: \".concat(Math.round(responseTime), \"ms\"));\n                        setLoadTime(Math.round(responseTime));\n                        setIsLoading(false);\n                        return;\n                    }\n                }\n                // Ultimate fallback\n                console.log(\"Using default timing\");\n                setLoadTime(750);\n                setIsLoading(false);\n            } catch (error) {\n                console.warn(\"Error measuring load time:\", error);\n                setLoadTime(750);\n                setIsLoading(false);\n            }\n        };\n        // Strategy: Wait for everything to be ready\n        const initMeasurement = ()=>{\n            if (document.readyState === \"complete\") {\n                // Page is already loaded, measure immediately with a small delay\n                setTimeout(measureLoadTime, 200);\n            } else {\n                // Wait for the load event\n                const handleLoad = ()=>{\n                    setTimeout(measureLoadTime, 200);\n                };\n                window.addEventListener(\"load\", handleLoad, {\n                    once: true\n                });\n                return ()=>window.removeEventListener(\"load\", handleLoad);\n            }\n        };\n        initMeasurement();\n    }, []);\n    const formatLoadTime = (time)=>{\n        if (!time || isNaN(time)) return \"N/A\";\n        if (time < 1000) {\n            return \"\".concat(time, \"ms\");\n        } else {\n            return \"\".concat((time / 1000).toFixed(1), \"s\");\n        }\n    };\n    const getPerformanceMessage = function(time) {\n        let isDomProcessing = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (!time || isNaN(time)) return \"\";\n        if (isDomProcessing) {\n            // DOM processing time thresholds (much lower)\n            if (time < 50) return \"Blazing fast!\";\n            if (time < 100) return \"Lightning quick!\";\n            if (time < 200) return \"Pretty speedy!\";\n            return \"Getting there!\";\n        } else {\n            // Full load time thresholds\n            if (time < 500) return \"Blazing fast!\";\n            if (time < 1000) return \"Lightning quick!\";\n            if (time < 2000) return \"Pretty speedy!\";\n            return \"Getting there!\";\n        }\n    };\n    const getDisplayText = function(time) {\n        let isDomProcessing = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (!time || isNaN(time)) return \"This website loaded in N/A\";\n        if (isDomProcessing && time < 200) {\n            // For fast DOM processing, show it as \"optimized performance\"\n            return \"This optimized website performs in ~\".concat(formatLoadTime(time));\n        } else {\n            // For everything else, show as load time\n            return \"This website loaded in ~\".concat(formatLoadTime(time));\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"inline-flex items-center space-x-2 \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-2 h-2 bg-current rounded-full animate-pulse\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm font-mono\",\n                    children: \"Measuring...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n            lineNumber: 131,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"inline-flex items-center space-x-2 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-2 h-2 bg-current rounded-full\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm font-mono\",\n                children: [\n                    \"DOM processed in \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: [\n                            \"~\",\n                            formatLoadTime(loadTime)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 26\n                    }, this),\n                    loadTime && !isNaN(loadTime) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"ml-2 opacity-80\",\n                        children: getPerformanceMessage(loadTime)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n        lineNumber: 139,\n        columnNumber: 5\n    }, this);\n}\n_s(LoadTimeDisplay, \"33yJbvgRUMyQorwGi4O5PbhnLNY=\");\n_c = LoadTimeDisplay;\nvar _c;\n$RefreshReg$(_c, \"LoadTimeDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3VpL0xvYWRUaW1lRGlzcGxheS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRTJDO0FBTTVCLFNBQVNFLGdCQUFnQixLQUF3QztRQUF4QyxFQUFFQyxZQUFZLEVBQUUsRUFBd0IsR0FBeEM7O0lBQ3RDLE1BQU0sQ0FBQ0MsVUFBVUMsWUFBWSxHQUFHTCwrQ0FBUUEsQ0FBZ0I7SUFDeEQsTUFBTSxDQUFDTSxXQUFXQyxhQUFhLEdBQUdQLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ1EsaUJBQWlCQyxtQkFBbUIsR0FBR1QsK0NBQVFBLENBQUM7SUFFdkRDLGdEQUFTQSxDQUFDO1FBQ1IsTUFBTVMsa0JBQWtCO1lBQ3RCLElBQUk7Z0JBQ0YsTUFBTUMsYUFBYUMsWUFBWUMsZ0JBQWdCLENBQUMsYUFBYSxDQUFDLEVBQUU7Z0JBRWhFLElBQUlGLFlBQVk7b0JBQ2QsbUVBQW1FO29CQUNuRSxtRUFBbUU7b0JBQ25FLElBQUlBLFdBQVdHLHdCQUF3QixHQUFHLEtBQUtILFdBQVdJLFdBQVcsR0FBRyxHQUFHO3dCQUN6RSxNQUFNQyxvQkFBb0JMLFdBQVdHLHdCQUF3QixHQUFHSCxXQUFXSSxXQUFXO3dCQUN0RkUsUUFBUUMsR0FBRyxDQUFDLHdCQUFzRCxPQUE5QkMsS0FBS0MsS0FBSyxDQUFDSixvQkFBbUI7d0JBQ2xFLElBQUlBLG9CQUFvQixLQUFLQSxvQkFBb0IsT0FBTzs0QkFDdERYLFlBQVljLEtBQUtDLEtBQUssQ0FBQ0o7NEJBQ3ZCVCxhQUFhOzRCQUNiO3dCQUNGO29CQUNGO29CQUVBLGlFQUFpRTtvQkFDakUsSUFBSUksV0FBV1UsY0FBYyxHQUFHLEtBQUtWLFdBQVdXLFVBQVUsSUFBSSxHQUFHO3dCQUMvRCxNQUFNbEIsV0FBV08sV0FBV1UsY0FBYyxHQUFHVixXQUFXVyxVQUFVO3dCQUNsRUwsUUFBUUMsR0FBRyxDQUFDLG1CQUF3QyxPQUFyQkMsS0FBS0MsS0FBSyxDQUFDaEIsV0FBVTt3QkFDcERDLFlBQVljLEtBQUtDLEtBQUssQ0FBQ2hCO3dCQUN2QkcsYUFBYTt3QkFDYjtvQkFDRjtvQkFFQSxvQ0FBb0M7b0JBQ3BDLElBQUlJLFdBQVdHLHdCQUF3QixHQUFHLEtBQUtILFdBQVdXLFVBQVUsSUFBSSxHQUFHO3dCQUN6RSxNQUFNQyxVQUFVWixXQUFXRyx3QkFBd0IsR0FBR0gsV0FBV1csVUFBVTt3QkFDM0VMLFFBQVFDLEdBQUcsQ0FBQywwQkFBOEMsT0FBcEJDLEtBQUtDLEtBQUssQ0FBQ0csVUFBUzt3QkFDMURsQixZQUFZYyxLQUFLQyxLQUFLLENBQUNHO3dCQUN2QmhCLGFBQWE7d0JBQ2I7b0JBQ0Y7b0JBRUEsZ0NBQWdDO29CQUNoQyxJQUFJSSxXQUFXSSxXQUFXLEdBQUcsS0FBS0osV0FBV1csVUFBVSxJQUFJLEdBQUc7d0JBQzVELE1BQU1FLGVBQWViLFdBQVdJLFdBQVcsR0FBR0osV0FBV1csVUFBVTt3QkFDbkVMLFFBQVFDLEdBQUcsQ0FBQyxrQkFBMkMsT0FBekJDLEtBQUtDLEtBQUssQ0FBQ0ksZUFBYzt3QkFDdkRuQixZQUFZYyxLQUFLQyxLQUFLLENBQUNJO3dCQUN2QmpCLGFBQWE7d0JBQ2I7b0JBQ0Y7Z0JBQ0Y7Z0JBRUEsb0JBQW9CO2dCQUNwQlUsUUFBUUMsR0FBRyxDQUFDO2dCQUNaYixZQUFZO2dCQUNaRSxhQUFhO1lBQ2YsRUFBRSxPQUFPa0IsT0FBTztnQkFDZFIsUUFBUVMsSUFBSSxDQUFDLDhCQUE4QkQ7Z0JBQzNDcEIsWUFBWTtnQkFDWkUsYUFBYTtZQUNmO1FBQ0Y7UUFFQSw0Q0FBNEM7UUFDNUMsTUFBTW9CLGtCQUFrQjtZQUN0QixJQUFJQyxTQUFTQyxVQUFVLEtBQUssWUFBWTtnQkFDdEMsaUVBQWlFO2dCQUNqRUMsV0FBV3BCLGlCQUFpQjtZQUM5QixPQUFPO2dCQUNMLDBCQUEwQjtnQkFDMUIsTUFBTXFCLGFBQWE7b0JBQ2pCRCxXQUFXcEIsaUJBQWlCO2dCQUM5QjtnQkFDQXNCLE9BQU9DLGdCQUFnQixDQUFDLFFBQVFGLFlBQVk7b0JBQUVHLE1BQU07Z0JBQUs7Z0JBQ3pELE9BQU8sSUFBTUYsT0FBT0csbUJBQW1CLENBQUMsUUFBUUo7WUFDbEQ7UUFDRjtRQUVBSjtJQUNGLEdBQUcsRUFBRTtJQUVMLE1BQU1TLGlCQUFpQixDQUFDQztRQUN0QixJQUFJLENBQUNBLFFBQVFDLE1BQU1ELE9BQU8sT0FBTztRQUVqQyxJQUFJQSxPQUFPLE1BQU07WUFDZixPQUFPLEdBQVEsT0FBTEEsTUFBSztRQUNqQixPQUFPO1lBQ0wsT0FBTyxHQUE0QixPQUF6QixDQUFDQSxPQUFPLElBQUcsRUFBR0UsT0FBTyxDQUFDLElBQUc7UUFDckM7SUFDRjtJQUVBLE1BQU1DLHdCQUF3QixTQUFDSDtZQUFxQjdCLG1GQUEyQjtRQUM3RSxJQUFJLENBQUM2QixRQUFRQyxNQUFNRCxPQUFPLE9BQU87UUFFakMsSUFBSTdCLGlCQUFpQjtZQUNuQiw4Q0FBOEM7WUFDOUMsSUFBSTZCLE9BQU8sSUFBSSxPQUFPO1lBQ3RCLElBQUlBLE9BQU8sS0FBSyxPQUFPO1lBQ3ZCLElBQUlBLE9BQU8sS0FBSyxPQUFPO1lBQ3ZCLE9BQU87UUFDVCxPQUFPO1lBQ0wsNEJBQTRCO1lBQzVCLElBQUlBLE9BQU8sS0FBSyxPQUFPO1lBQ3ZCLElBQUlBLE9BQU8sTUFBTSxPQUFPO1lBQ3hCLElBQUlBLE9BQU8sTUFBTSxPQUFPO1lBQ3hCLE9BQU87UUFDVDtJQUNGO0lBRUEsTUFBTUksaUJBQWlCLFNBQUNKO1lBQXFCN0IsbUZBQTJCO1FBQ3RFLElBQUksQ0FBQzZCLFFBQVFDLE1BQU1ELE9BQU8sT0FBTztRQUVqQyxJQUFJN0IsbUJBQW1CNkIsT0FBTyxLQUFLO1lBQ2pDLDhEQUE4RDtZQUM5RCxPQUFPLHVDQUE0RCxPQUFyQkQsZUFBZUM7UUFDL0QsT0FBTztZQUNMLHlDQUF5QztZQUN6QyxPQUFPLDJCQUFnRCxPQUFyQkQsZUFBZUM7UUFDbkQ7SUFDRjtJQUVBLElBQUkvQixXQUFXO1FBQ2IscUJBQ0UsOERBQUNvQztZQUFJdkMsV0FBVyxzQ0FBZ0QsT0FBVkE7OzhCQUNwRCw4REFBQ3VDO29CQUFJdkMsV0FBVTs7Ozs7OzhCQUNmLDhEQUFDd0M7b0JBQUt4QyxXQUFVOzhCQUFvQjs7Ozs7Ozs7Ozs7O0lBRzFDO0lBRUEscUJBQ0UsOERBQUN1QztRQUFJdkMsV0FBVyxzQ0FBZ0QsT0FBVkE7OzBCQUNwRCw4REFBQ3VDO2dCQUFJdkMsV0FBVTs7Ozs7OzBCQUNmLDhEQUFDd0M7Z0JBQUt4QyxXQUFVOztvQkFBb0I7a0NBQ2pCLDhEQUFDeUM7OzRCQUFPOzRCQUFFUixlQUFlaEM7Ozs7Ozs7b0JBQ3pDQSxZQUFZLENBQUNrQyxNQUFNbEMsMkJBQ2xCLDhEQUFDdUM7d0JBQUt4QyxXQUFVO2tDQUNicUMsc0JBQXNCcEM7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU1uQztHQTlJd0JGO0tBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL3VpL0xvYWRUaW1lRGlzcGxheS50c3g/ODkyNCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0J1xuXG5pbnRlcmZhY2UgTG9hZFRpbWVEaXNwbGF5UHJvcHMge1xuICBjbGFzc05hbWU/OiBzdHJpbmdcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTG9hZFRpbWVEaXNwbGF5KHsgY2xhc3NOYW1lID0gJycgfTogTG9hZFRpbWVEaXNwbGF5UHJvcHMpIHtcbiAgY29uc3QgW2xvYWRUaW1lLCBzZXRMb2FkVGltZV0gPSB1c2VTdGF0ZTxudW1iZXIgfCBudWxsPihudWxsKVxuICBjb25zdCBbaXNMb2FkaW5nLCBzZXRJc0xvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSlcbiAgY29uc3QgW2lzRG9tUHJvY2Vzc2luZywgc2V0SXNEb21Qcm9jZXNzaW5nXSA9IHVzZVN0YXRlKGZhbHNlKVxuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgbWVhc3VyZUxvYWRUaW1lID0gKCkgPT4ge1xuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3QgbmF2aWdhdGlvbiA9IHBlcmZvcm1hbmNlLmdldEVudHJpZXNCeVR5cGUoJ25hdmlnYXRpb24nKVswXSBhcyBQZXJmb3JtYW5jZU5hdmlnYXRpb25UaW1pbmdcblxuICAgICAgICBpZiAobmF2aWdhdGlvbikge1xuICAgICAgICAgIC8vIE9wdGlvbiAxOiBET00gUHJvY2Vzc2luZyBUaW1lICh3ZWJzaXRlIHBlcmZvcm1hbmNlLCBub3QgbmV0d29yaylcbiAgICAgICAgICAvLyBUaGlzIG1lYXN1cmVzIGhvdyBmYXN0IHlvdXIgd2Vic2l0ZSBwcm9jZXNzZXMgYWZ0ZXIgSFRNTCBhcnJpdmVzXG4gICAgICAgICAgaWYgKG5hdmlnYXRpb24uZG9tQ29udGVudExvYWRlZEV2ZW50RW5kID4gMCAmJiBuYXZpZ2F0aW9uLnJlc3BvbnNlRW5kID4gMCkge1xuICAgICAgICAgICAgY29uc3QgZG9tUHJvY2Vzc2luZ1RpbWUgPSBuYXZpZ2F0aW9uLmRvbUNvbnRlbnRMb2FkZWRFdmVudEVuZCAtIG5hdmlnYXRpb24ucmVzcG9uc2VFbmRcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGBET00gcHJvY2Vzc2luZyB0aW1lOiAke01hdGgucm91bmQoZG9tUHJvY2Vzc2luZ1RpbWUpfW1zICh3ZWJzaXRlIHBlcmZvcm1hbmNlKWApXG4gICAgICAgICAgICBpZiAoZG9tUHJvY2Vzc2luZ1RpbWUgPiAwICYmIGRvbVByb2Nlc3NpbmdUaW1lIDwgMTAwMDApIHtcbiAgICAgICAgICAgICAgc2V0TG9hZFRpbWUoTWF0aC5yb3VuZChkb21Qcm9jZXNzaW5nVGltZSkpXG4gICAgICAgICAgICAgIHNldElzTG9hZGluZyhmYWxzZSlcbiAgICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgLy8gT3B0aW9uIDI6IEZ1bGwgTG9hZCBUaW1lIChpbmNsdWRlcyBuZXR3b3JrIC0gY3VycmVudCBhcHByb2FjaClcbiAgICAgICAgICBpZiAobmF2aWdhdGlvbi5sb2FkRXZlbnRTdGFydCA+IDAgJiYgbmF2aWdhdGlvbi5mZXRjaFN0YXJ0ID49IDApIHtcbiAgICAgICAgICAgIGNvbnN0IGxvYWRUaW1lID0gbmF2aWdhdGlvbi5sb2FkRXZlbnRTdGFydCAtIG5hdmlnYXRpb24uZmV0Y2hTdGFydFxuICAgICAgICAgICAgY29uc29sZS5sb2coYEZ1bGwgbG9hZCB0aW1lOiAke01hdGgucm91bmQobG9hZFRpbWUpfW1zIChpbmNsdWRlcyBuZXR3b3JrKWApXG4gICAgICAgICAgICBzZXRMb2FkVGltZShNYXRoLnJvdW5kKGxvYWRUaW1lKSlcbiAgICAgICAgICAgIHNldElzTG9hZGluZyhmYWxzZSlcbiAgICAgICAgICAgIHJldHVyblxuICAgICAgICAgIH1cblxuICAgICAgICAgIC8vIEZhbGxiYWNrOiBET01Db250ZW50TG9hZGVkIHRpbWluZ1xuICAgICAgICAgIGlmIChuYXZpZ2F0aW9uLmRvbUNvbnRlbnRMb2FkZWRFdmVudEVuZCA+IDAgJiYgbmF2aWdhdGlvbi5mZXRjaFN0YXJ0ID49IDApIHtcbiAgICAgICAgICAgIGNvbnN0IGRvbVRpbWUgPSBuYXZpZ2F0aW9uLmRvbUNvbnRlbnRMb2FkZWRFdmVudEVuZCAtIG5hdmlnYXRpb24uZmV0Y2hTdGFydFxuICAgICAgICAgICAgY29uc29sZS5sb2coYERPTUNvbnRlbnRMb2FkZWQgdGltZTogJHtNYXRoLnJvdW5kKGRvbVRpbWUpfW1zYClcbiAgICAgICAgICAgIHNldExvYWRUaW1lKE1hdGgucm91bmQoZG9tVGltZSkpXG4gICAgICAgICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpXG4gICAgICAgICAgICByZXR1cm5cbiAgICAgICAgICB9XG5cbiAgICAgICAgICAvLyBGaW5hbCBmYWxsYmFjazogUmVzcG9uc2UgdGltZVxuICAgICAgICAgIGlmIChuYXZpZ2F0aW9uLnJlc3BvbnNlRW5kID4gMCAmJiBuYXZpZ2F0aW9uLmZldGNoU3RhcnQgPj0gMCkge1xuICAgICAgICAgICAgY29uc3QgcmVzcG9uc2VUaW1lID0gbmF2aWdhdGlvbi5yZXNwb25zZUVuZCAtIG5hdmlnYXRpb24uZmV0Y2hTdGFydFxuICAgICAgICAgICAgY29uc29sZS5sb2coYFJlc3BvbnNlIHRpbWU6ICR7TWF0aC5yb3VuZChyZXNwb25zZVRpbWUpfW1zYClcbiAgICAgICAgICAgIHNldExvYWRUaW1lKE1hdGgucm91bmQocmVzcG9uc2VUaW1lKSlcbiAgICAgICAgICAgIHNldElzTG9hZGluZyhmYWxzZSlcbiAgICAgICAgICAgIHJldHVyblxuICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgIC8vIFVsdGltYXRlIGZhbGxiYWNrXG4gICAgICAgIGNvbnNvbGUubG9nKCdVc2luZyBkZWZhdWx0IHRpbWluZycpXG4gICAgICAgIHNldExvYWRUaW1lKDc1MClcbiAgICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKVxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS53YXJuKCdFcnJvciBtZWFzdXJpbmcgbG9hZCB0aW1lOicsIGVycm9yKVxuICAgICAgICBzZXRMb2FkVGltZSg3NTApXG4gICAgICAgIHNldElzTG9hZGluZyhmYWxzZSlcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBTdHJhdGVneTogV2FpdCBmb3IgZXZlcnl0aGluZyB0byBiZSByZWFkeVxuICAgIGNvbnN0IGluaXRNZWFzdXJlbWVudCA9ICgpID0+IHtcbiAgICAgIGlmIChkb2N1bWVudC5yZWFkeVN0YXRlID09PSAnY29tcGxldGUnKSB7XG4gICAgICAgIC8vIFBhZ2UgaXMgYWxyZWFkeSBsb2FkZWQsIG1lYXN1cmUgaW1tZWRpYXRlbHkgd2l0aCBhIHNtYWxsIGRlbGF5XG4gICAgICAgIHNldFRpbWVvdXQobWVhc3VyZUxvYWRUaW1lLCAyMDApXG4gICAgICB9IGVsc2Uge1xuICAgICAgICAvLyBXYWl0IGZvciB0aGUgbG9hZCBldmVudFxuICAgICAgICBjb25zdCBoYW5kbGVMb2FkID0gKCkgPT4ge1xuICAgICAgICAgIHNldFRpbWVvdXQobWVhc3VyZUxvYWRUaW1lLCAyMDApXG4gICAgICAgIH1cbiAgICAgICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ2xvYWQnLCBoYW5kbGVMb2FkLCB7IG9uY2U6IHRydWUgfSlcbiAgICAgICAgcmV0dXJuICgpID0+IHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdsb2FkJywgaGFuZGxlTG9hZClcbiAgICAgIH1cbiAgICB9XG5cbiAgICBpbml0TWVhc3VyZW1lbnQoKVxuICB9LCBbXSlcblxuICBjb25zdCBmb3JtYXRMb2FkVGltZSA9ICh0aW1lOiBudW1iZXIgfCBudWxsKSA9PiB7XG4gICAgaWYgKCF0aW1lIHx8IGlzTmFOKHRpbWUpKSByZXR1cm4gJ04vQSdcblxuICAgIGlmICh0aW1lIDwgMTAwMCkge1xuICAgICAgcmV0dXJuIGAke3RpbWV9bXNgXG4gICAgfSBlbHNlIHtcbiAgICAgIHJldHVybiBgJHsodGltZSAvIDEwMDApLnRvRml4ZWQoMSl9c2BcbiAgICB9XG4gIH1cblxuICBjb25zdCBnZXRQZXJmb3JtYW5jZU1lc3NhZ2UgPSAodGltZTogbnVtYmVyIHwgbnVsbCwgaXNEb21Qcm9jZXNzaW5nOiBib29sZWFuID0gZmFsc2UpID0+IHtcbiAgICBpZiAoIXRpbWUgfHwgaXNOYU4odGltZSkpIHJldHVybiAnJ1xuXG4gICAgaWYgKGlzRG9tUHJvY2Vzc2luZykge1xuICAgICAgLy8gRE9NIHByb2Nlc3NpbmcgdGltZSB0aHJlc2hvbGRzIChtdWNoIGxvd2VyKVxuICAgICAgaWYgKHRpbWUgPCA1MCkgcmV0dXJuICdCbGF6aW5nIGZhc3QhJ1xuICAgICAgaWYgKHRpbWUgPCAxMDApIHJldHVybiAnTGlnaHRuaW5nIHF1aWNrISdcbiAgICAgIGlmICh0aW1lIDwgMjAwKSByZXR1cm4gJ1ByZXR0eSBzcGVlZHkhJ1xuICAgICAgcmV0dXJuICdHZXR0aW5nIHRoZXJlISdcbiAgICB9IGVsc2Uge1xuICAgICAgLy8gRnVsbCBsb2FkIHRpbWUgdGhyZXNob2xkc1xuICAgICAgaWYgKHRpbWUgPCA1MDApIHJldHVybiAnQmxhemluZyBmYXN0ISdcbiAgICAgIGlmICh0aW1lIDwgMTAwMCkgcmV0dXJuICdMaWdodG5pbmcgcXVpY2shJ1xuICAgICAgaWYgKHRpbWUgPCAyMDAwKSByZXR1cm4gJ1ByZXR0eSBzcGVlZHkhJ1xuICAgICAgcmV0dXJuICdHZXR0aW5nIHRoZXJlISdcbiAgICB9XG4gIH1cblxuICBjb25zdCBnZXREaXNwbGF5VGV4dCA9ICh0aW1lOiBudW1iZXIgfCBudWxsLCBpc0RvbVByb2Nlc3Npbmc6IGJvb2xlYW4gPSBmYWxzZSkgPT4ge1xuICAgIGlmICghdGltZSB8fCBpc05hTih0aW1lKSkgcmV0dXJuICdUaGlzIHdlYnNpdGUgbG9hZGVkIGluIE4vQSdcblxuICAgIGlmIChpc0RvbVByb2Nlc3NpbmcgJiYgdGltZSA8IDIwMCkge1xuICAgICAgLy8gRm9yIGZhc3QgRE9NIHByb2Nlc3NpbmcsIHNob3cgaXQgYXMgXCJvcHRpbWl6ZWQgcGVyZm9ybWFuY2VcIlxuICAgICAgcmV0dXJuIGBUaGlzIG9wdGltaXplZCB3ZWJzaXRlIHBlcmZvcm1zIGluIH4ke2Zvcm1hdExvYWRUaW1lKHRpbWUpfWBcbiAgICB9IGVsc2Uge1xuICAgICAgLy8gRm9yIGV2ZXJ5dGhpbmcgZWxzZSwgc2hvdyBhcyBsb2FkIHRpbWVcbiAgICAgIHJldHVybiBgVGhpcyB3ZWJzaXRlIGxvYWRlZCBpbiB+JHtmb3JtYXRMb2FkVGltZSh0aW1lKX1gXG4gICAgfVxuICB9XG5cbiAgaWYgKGlzTG9hZGluZykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT17YGlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgJHtjbGFzc05hbWV9YH0+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yIGgtMiBiZy1jdXJyZW50IHJvdW5kZWQtZnVsbCBhbmltYXRlLXB1bHNlXCI+PC9kaXY+XG4gICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tb25vXCI+TWVhc3VyaW5nLi4uPC9zcGFuPlxuICAgICAgPC9kaXY+XG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT17YGlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgJHtjbGFzc05hbWV9YH0+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMiBoLTIgYmctY3VycmVudCByb3VuZGVkLWZ1bGxcIj48L2Rpdj5cbiAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tb25vXCI+XG4gICAgICAgIERPTSBwcm9jZXNzZWQgaW4gPHN0cm9uZz5+e2Zvcm1hdExvYWRUaW1lKGxvYWRUaW1lKX08L3N0cm9uZz5cbiAgICAgICAge2xvYWRUaW1lICYmICFpc05hTihsb2FkVGltZSkgJiYgKFxuICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1sLTIgb3BhY2l0eS04MFwiPlxuICAgICAgICAgICAge2dldFBlcmZvcm1hbmNlTWVzc2FnZShsb2FkVGltZSl9XG4gICAgICAgICAgPC9zcGFuPlxuICAgICAgICApfVxuICAgICAgPC9zcGFuPlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJMb2FkVGltZURpc3BsYXkiLCJjbGFzc05hbWUiLCJsb2FkVGltZSIsInNldExvYWRUaW1lIiwiaXNMb2FkaW5nIiwic2V0SXNMb2FkaW5nIiwiaXNEb21Qcm9jZXNzaW5nIiwic2V0SXNEb21Qcm9jZXNzaW5nIiwibWVhc3VyZUxvYWRUaW1lIiwibmF2aWdhdGlvbiIsInBlcmZvcm1hbmNlIiwiZ2V0RW50cmllc0J5VHlwZSIsImRvbUNvbnRlbnRMb2FkZWRFdmVudEVuZCIsInJlc3BvbnNlRW5kIiwiZG9tUHJvY2Vzc2luZ1RpbWUiLCJjb25zb2xlIiwibG9nIiwiTWF0aCIsInJvdW5kIiwibG9hZEV2ZW50U3RhcnQiLCJmZXRjaFN0YXJ0IiwiZG9tVGltZSIsInJlc3BvbnNlVGltZSIsImVycm9yIiwid2FybiIsImluaXRNZWFzdXJlbWVudCIsImRvY3VtZW50IiwicmVhZHlTdGF0ZSIsInNldFRpbWVvdXQiLCJoYW5kbGVMb2FkIiwid2luZG93IiwiYWRkRXZlbnRMaXN0ZW5lciIsIm9uY2UiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwiZm9ybWF0TG9hZFRpbWUiLCJ0aW1lIiwiaXNOYU4iLCJ0b0ZpeGVkIiwiZ2V0UGVyZm9ybWFuY2VNZXNzYWdlIiwiZ2V0RGlzcGxheVRleHQiLCJkaXYiLCJzcGFuIiwic3Ryb25nIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/LoadTimeDisplay.tsx\n"));

/***/ })

});