"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ui/LoadTimeDisplay.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/LoadTimeDisplay.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LoadTimeDisplay; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction LoadTimeDisplay(param) {\n    let { className = \"\" } = param;\n    _s();\n    const [metrics, setMetrics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        domProcessing: null,\n        networkTime: null,\n        totalLoad: null\n    });\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const measurePerformance = ()=>{\n            try {\n                const navigation = performance.getEntriesByType(\"navigation\")[0];\n                if (navigation) {\n                    const newMetrics = {\n                        domProcessing: null,\n                        networkTime: null,\n                        totalLoad: null\n                    };\n                    // DOM Processing Time (website optimization)\n                    if (navigation.domContentLoadedEventEnd > 0 && navigation.responseEnd > 0) {\n                        newMetrics.domProcessing = Math.round(navigation.domContentLoadedEventEnd - navigation.responseEnd);\n                    }\n                    // Network Time (connection quality)\n                    if (navigation.responseEnd > 0 && navigation.fetchStart >= 0) {\n                        newMetrics.networkTime = Math.round(navigation.responseEnd - navigation.fetchStart);\n                    }\n                    // Total Load Time (overall experience)\n                    if (navigation.loadEventStart > 0 && navigation.fetchStart >= 0) {\n                        newMetrics.totalLoad = Math.round(navigation.loadEventStart - navigation.fetchStart);\n                    }\n                    console.log(\"Performance metrics:\", newMetrics);\n                    setMetrics(newMetrics);\n                    setIsLoading(false);\n                } else {\n                    // Fallback metrics\n                    setMetrics({\n                        domProcessing: 50,\n                        networkTime: 150,\n                        totalLoad: 750\n                    });\n                    setIsLoading(false);\n                }\n            } catch (error) {\n                console.warn(\"Error measuring performance:\", error);\n                setMetrics({\n                    domProcessing: 50,\n                    networkTime: 150,\n                    totalLoad: 750\n                });\n                setIsLoading(false);\n            }\n        };\n        // Strategy: Wait for everything to be ready\n        const initMeasurement = ()=>{\n            if (document.readyState === \"complete\") {\n                // Page is already loaded, measure immediately with a small delay\n                setTimeout(measurePerformance, 200);\n            } else {\n                // Wait for the load event\n                const handleLoad = ()=>{\n                    setTimeout(measurePerformance, 200);\n                };\n                window.addEventListener(\"load\", handleLoad, {\n                    once: true\n                });\n                return ()=>window.removeEventListener(\"load\", handleLoad);\n            }\n        };\n        initMeasurement();\n    }, []);\n    const formatLoadTime = (time)=>{\n        if (!time || isNaN(time)) return \"N/A\";\n        if (time < 1000) {\n            return \"\".concat(time, \"ms\");\n        } else {\n            return \"\".concat((time / 1000).toFixed(1), \"s\");\n        }\n    };\n    const getPerformanceMessage = function(time) {\n        let isDomProcessing1 = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (!time || isNaN(time)) return \"\";\n        if (isDomProcessing1) {\n            // DOM processing time thresholds (much lower)\n            if (time < 50) return \"Blazing fast!\";\n            if (time < 100) return \"Lightning quick!\";\n            if (time < 200) return \"Pretty speedy!\";\n            return \"Getting there!\";\n        } else {\n            // Full load time thresholds\n            if (time < 500) return \"Blazing fast!\";\n            if (time < 1000) return \"Lightning quick!\";\n            if (time < 2000) return \"Pretty speedy!\";\n            return \"Getting there!\";\n        }\n    };\n    const getDisplayText = function(time) {\n        let isDomProcessing1 = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (!time || isNaN(time)) return \"This website loaded in N/A\";\n        if (isDomProcessing1 && time < 200) {\n            // For fast DOM processing, show it as \"optimized performance\"\n            return \"This optimized website performs in ~\".concat(formatLoadTime(time));\n        } else {\n            // For everything else, show as load time\n            return \"This website loaded in ~\".concat(formatLoadTime(time));\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"inline-flex items-center space-x-2 \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-2 h-2 bg-current rounded-full animate-pulse\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm font-mono\",\n                    children: \"Measuring...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n            lineNumber: 133,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"inline-flex items-center space-x-2 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-2 h-2 bg-current rounded-full\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm font-mono\",\n                children: [\n                    getDisplayText(loadTime, isDomProcessing),\n                    loadTime && !isNaN(loadTime) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"ml-2 opacity-80\",\n                        children: getPerformanceMessage(loadTime, isDomProcessing)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\ui\\\\LoadTimeDisplay.tsx\",\n        lineNumber: 141,\n        columnNumber: 5\n    }, this);\n}\n_s(LoadTimeDisplay, \"6FitVEwf91HySuhJ+VaOEnPX+Os=\");\n_c = LoadTimeDisplay;\nvar _c;\n$RefreshReg$(_c, \"LoadTimeDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/LoadTimeDisplay.tsx\n"));

/***/ })

});